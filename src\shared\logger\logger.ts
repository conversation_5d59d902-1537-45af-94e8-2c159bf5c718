import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import { env } from '../config/env';

const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Configurações baseadas no ambiente
const isDevelopment = env.NODE_ENV === 'development';
const isProduction = env.NODE_ENV === 'production';

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Formato estruturado para logs
const structuredFormat = winston.format.combine(
  winston.format.errors({ stack: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.printf(
    ({
      timestamp,
      level,
      message,
      service,
      traceId,
      spanId,
      userId,
      requestId,
      duration,
      statusCode,
      method,
      path,
      ...meta
    }) => {
      const logEntry = {
        timestamp,
        level,
        message,
        service: service || 'fastify-api',
        environment: env.NODE_ENV,
        ...(traceId && { traceId }),
        ...(spanId && { spanId }),
        ...(userId && { userId }),
        ...(requestId && { requestId }),
        ...(duration && { duration }),
        ...(statusCode && { statusCode }),
        ...(method && { method }),
        ...(path && { path }),
        ...meta,
      };
      return JSON.stringify(logEntry);
    }
  )
);

// Formato para desenvolvimento (mais legível)
const developmentFormat = winston.format.combine(
  winston.format.errors({ stack: true }),
  winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
  winston.format.colorize(),
  winston.format.printf(
    ({
      timestamp,
      level,
      message,
      service,
      traceId,
      userId,
      requestId,
      duration,
      statusCode,
      method,
      path,
      ...meta
    }) => {
      let logMessage = `${timestamp} [${level}]`;

      if (service) logMessage += ` [${service}]`;
      if (traceId) logMessage += ` [trace:${traceId.substring(0, 8)}]`;
      if (requestId) logMessage += ` [req:${requestId.substring(0, 8)}]`;
      if (userId) logMessage += ` [user:${userId}]`;

      logMessage += `: ${message}`;

      if (method && path) logMessage += ` ${method} ${path}`;
      if (statusCode) logMessage += ` [${statusCode}]`;
      if (duration) logMessage += ` (${duration}ms)`;

      if (Object.keys(meta).length > 0) {
        logMessage += ` ${JSON.stringify(meta)}`;
      }

      return logMessage;
    }
  )
);

const fileRotateTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'application-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  format: structuredFormat,
});

// Transport separado para erros
const errorFileTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '30d',
  level: 'error',
  format: structuredFormat,
});

const logger = winston.createLogger({
  levels: logLevels,
  level: isDevelopment ? 'debug' : 'info',
  format: structuredFormat,
  transports: [
    new winston.transports.Console({
      format: isDevelopment ? developmentFormat : structuredFormat,
    }),
    fileRotateTransport,
    errorFileTransport,
  ],
  // Configurações adicionais
  exitOnError: false,
  silent: process.env.NODE_ENV === 'test',
});

// Adicionar métodos de conveniência para logs estruturados
interface StructuredLogData {
  traceId?: string;
  spanId?: string;
  userId?: string;
  requestId?: string;
  duration?: number;
  statusCode?: number;
  method?: string;
  path?: string;
  service?: string;
  [key: string]: any;
}

class StructuredLogger {
  private logger: winston.Logger;

  constructor(logger: winston.Logger) {
    this.logger = logger;
  }

  info(message: string, data?: StructuredLogData) {
    this.logger.info(message, data);
  }

  warn(message: string, data?: StructuredLogData) {
    this.logger.warn(message, data);
  }

  error(message: string, data?: StructuredLogData) {
    this.logger.error(message, data);
  }

  debug(message: string, data?: StructuredLogData) {
    this.logger.debug(message, data);
  }

  http(message: string, data?: StructuredLogData) {
    this.logger.http(message, data);
  }

  // Método específico para logs de requisições HTTP
  logRequest(data: {
    method: string;
    path: string;
    statusCode: number;
    duration: number;
    userId?: string;
    traceId?: string;
    requestId?: string;
    userAgent?: string;
    ip?: string;
  }) {
    const { method, path, statusCode, duration, ...meta } = data;
    const message = `${method} ${path} ${statusCode} - ${duration}ms`;

    this.http(message, {
      method,
      path,
      statusCode,
      duration,
      ...meta,
    });
  }

  // Método específico para logs de erros de aplicação
  logError(error: Error, context?: StructuredLogData) {
    this.error(error.message, {
      stack: error.stack,
      name: error.name,
      ...context,
    });
  }

  // Método específico para logs de operações de banco de dados
  logDatabaseOperation(
    operation: string,
    duration: number,
    success: boolean,
    context?: StructuredLogData
  ) {
    const message = `Database ${operation} ${success ? 'completed' : 'failed'} in ${duration}ms`;

    if (success) {
      this.debug(message, { operation, duration, success, ...context });
    } else {
      this.warn(message, { operation, duration, success, ...context });
    }
  }
}

const structuredLogger = new StructuredLogger(logger);

export default structuredLogger;
export { logger as winstonLogger };
