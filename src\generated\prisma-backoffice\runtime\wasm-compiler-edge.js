
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */
"use strict";var cu=Object.create;var Jr=Object.defineProperty;var uu=Object.getOwnPropertyDescriptor;var pu=Object.getOwnPropertyNames;var mu=Object.getPrototypeOf,du=Object.prototype.hasOwnProperty;var ge=(e,t)=>()=>(e&&(t=e(e=0)),t);var se=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),pt=(e,t)=>{for(var r in t)Jr(e,r,{get:t[r],enumerable:!0})},Fo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of pu(t))!du.call(e,i)&&i!==r&&Jr(e,i,{get:()=>t[i],enumerable:!(n=uu(t,i))||n.enumerable});return e};var Ce=(e,t,r)=>(r=e!=null?cu(mu(e)):{},Fo(t||!e||!e.__esModule?Jr(r,"default",{value:e,enumerable:!0}):r,e)),$o=e=>Fo(Jr({},"__esModule",{value:!0}),e);function li(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new w(hu.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new w([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new w([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new w(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new w(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}qo(`encoding "${t}"`)}function fu(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(h=0){return Z(h,"offset"),me(h,"offset"),ee(h,"offset",this.length-1),new DataView(this.buffer)[r[a]](h,f)},o=(a,f)=>function(h,A=0){let C=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),S=yu[C];return Z(A,"offset"),me(A,"offset"),ee(A,"offset",this.length-1),gu(h,"value",S[0],S[1]),new DataView(this.buffer)[r[a]](A,h,f),A+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(e[a]=i(f,!1),e[a+"LE"]=i(f,!0),e[a+"BE"]=i(f,!1)),a.startsWith("write")&&(e[a]=o(f,!1),e[a+"LE"]=o(f,!0),e[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function qo(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function Kr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function ee(e,t,r=Eu+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function Z(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function me(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function gu(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function Vo(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function xu(e,t="utf8"){return w.from(e,t)}var w,yu,hu,wu,bu,Eu,y,ci,c=ge(()=>{"use strict";w=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return Vo(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return li(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return bu.includes(t)}static compare(t,r){Kr(t,"buff1"),Kr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return li(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){Z(t,"offset"),me(t,"offset"),ee(t,"offset",this.length-1),Z(r,"byteLength"),me(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){Z(t,"offset"),me(t,"offset"),ee(t,"offset",this.length-1),Z(r,"byteLength"),me(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){Z(t,"offset"),me(t,"offset"),ee(t,"offset",this.length-1),Z(r,"byteLength"),me(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){Z(t,"offset"),me(t,"offset"),ee(t,"offset",this.length-1),Z(r,"byteLength"),me(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){Z(r,"offset"),me(r,"offset"),ee(r,"offset",this.length-1),Z(n,"byteLength"),me(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){Z(r,"offset"),me(r,"offset"),ee(r,"offset",this.length-1),Z(n,"byteLength"),me(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return Kr(t,"target"),Z(r,"targetStart"),Z(n,"targetEnd"),Z(i,"sourceStart"),Z(o,"sourceEnd"),ee(r,"targetStart"),ee(n,"targetEnd",t.length),ee(i,"sourceStart"),ee(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return Kr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){ee(r,"targetStart"),ee(n,"sourceStart",this.length),ee(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,Z(o,"offset"),Z(s,"length"),ee(o,"offset",this.length),ee(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),li(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),Vo(i,"encoding"),ee(o,"offset",this.length),ee(s,"end",this.length),t.length!==0)for(let a=o;a<s;a+=t.length)super.set(t.slice(0,t.length+a>=this.length?this.length-a:t.length),a);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,h)=>(i?h<=a:h>=a)&&this[h]===s[0]&&s.every((C,S)=>this[h+S]===C))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return wu.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+ci(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+ci(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?ci(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");qo(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};yu={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},hu=new TextEncoder,wu=new TextDecoder,bu=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],Eu=4294967295;fu(w.prototype);y=new Proxy(xu,{construct(e,[t,r]){return w.from(t,r)},get(e,t){return w[t]}}),ci=String.fromCodePoint});var g,x,u=ge(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4},{cwd:x}=g});var b,p=ge(()=>{"use strict";b=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var E,m=ge(()=>{"use strict";E=()=>{};E.prototype=E});var d=ge(()=>{"use strict"});function Ho(e,t){var r,n,i,o,s,a,f,h,A=e.constructor,C=A.precision;if(!e.s||!t.s)return t.s||(t=new A(e)),W?V(t,C):t;if(f=e.d,h=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=h.length):(n=h,i=s,a=f.length),s=Math.ceil(C/Q),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=h.length,a-o<0&&(o=a,n=h,h=f,f=n),r=0;o;)r=(f[--o]=f[o]+h[o]+r)/te|0,f[o]%=te;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return t.d=f,t.e=i,W?V(t,C):t}function Se(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Ye+e)}function Re(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=Q-n.length,r&&(o+=Ve(r)),o+=n;s=e[t],n=s+"",r=Q-n.length,r&&(o+=Ve(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function Go(e,t){var r,n,i,o,s,a,f=0,h=0,A=e.constructor,C=A.precision;if(X(e)>16)throw Error(pi+X(e));if(!e.s)return new A(ye);for(t==null?(W=!1,a=C):a=t,s=new A(.03125);e.abs().gte(.1);)e=e.times(s),h+=5;for(n=Math.log(ze(2,h))/Math.LN10*2+5|0,a+=n,r=i=o=new A(ye),A.precision=a;;){if(i=V(i.times(e),a),r=r.times(++f),s=o.plus(Le(i,r,a)),Re(s.d).slice(0,a)===Re(o.d).slice(0,a)){for(;h--;)o=V(o.times(o),a);return A.precision=C,t==null?(W=!0,V(o,C)):o}o=s}}function X(e){for(var t=e.e*Q,r=e.d[0];r>=10;r/=10)t++;return t}function ui(e,t,r){if(t>e.LN10.sd())throw W=!0,r&&(e.precision=r),Error(be+"LN10 precision limit exceeded");return V(new e(e.LN10),t)}function Ve(e){for(var t="";e--;)t+="0";return t}function Gt(e,t){var r,n,i,o,s,a,f,h,A,C=1,S=10,R=e,_=R.d,k=R.constructor,L=k.precision;if(R.s<1)throw Error(be+(R.s?"NaN":"-Infinity"));if(R.eq(ye))return new k(0);if(t==null?(W=!1,h=L):h=t,R.eq(10))return t==null&&(W=!0),ui(k,h);if(h+=S,k.precision=h,r=Re(_),n=r.charAt(0),o=X(R),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)R=R.times(e),r=Re(R.d),n=r.charAt(0),C++;o=X(R),n>1?(R=new k("0."+r),o++):R=new k(n+"."+r.slice(1))}else return f=ui(k,h+2,L).times(o+""),R=Gt(new k(n+"."+r.slice(1)),h-S).plus(f),k.precision=L,t==null?(W=!0,V(R,L)):R;for(a=s=R=Le(R.minus(ye),R.plus(ye),h),A=V(R.times(R),h),i=3;;){if(s=V(s.times(A),h),f=a.plus(Le(s,new k(i),h)),Re(f.d).slice(0,h)===Re(a.d).slice(0,h))return a=a.times(2),o!==0&&(a=a.plus(ui(k,h+2,L).times(o+""))),a=Le(a,new k(C),h),k.precision=L,t==null?(W=!0,V(a,L)):a;a=f,i+=2}}function Bo(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=dt(r/Q),e.d=[],n=(r+1)%Q,r<0&&(n+=Q),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=Q;n<i;)e.d.push(+t.slice(n,n+=Q));t=t.slice(n),n=Q-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),W&&(e.e>zr||e.e<-zr))throw Error(pi+r)}else e.s=0,e.e=0,e.d=[0];return e}function V(e,t,r){var n,i,o,s,a,f,h,A,C=e.d;for(s=1,o=C[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=Q,i=t,h=C[A=0];else{if(A=Math.ceil((n+1)/Q),o=C.length,A>=o)return e;for(h=o=C[A],s=1;o>=10;o/=10)s++;n%=Q,i=n-Q+s}if(r!==void 0&&(o=ze(10,s-i-1),a=h/o%10|0,f=t<0||C[A+1]!==void 0||h%o,f=r<4?(a||f)&&(r==0||r==(e.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?h/ze(10,s-i):0:C[A-1])%10&1||r==(e.s<0?8:7))),t<1||!C[0])return f?(o=X(e),C.length=1,t=t-o-1,C[0]=ze(10,(Q-t%Q)%Q),e.e=dt(-t/Q)||0):(C.length=1,C[0]=e.e=e.s=0),e;if(n==0?(C.length=A,o=1,A--):(C.length=A+1,o=ze(10,Q-n),C[A]=i>0?(h/ze(10,s-i)%ze(10,i)|0)*o:0),f)for(;;)if(A==0){(C[0]+=o)==te&&(C[0]=1,++e.e);break}else{if(C[A]+=o,C[A]!=te)break;C[A--]=0,o=1}for(n=C.length;C[--n]===0;)C.pop();if(W&&(e.e>zr||e.e<-zr))throw Error(pi+X(e));return e}function Wo(e,t){var r,n,i,o,s,a,f,h,A,C,S=e.constructor,R=S.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new S(e),W?V(t,R):t;if(f=e.d,C=t.d,n=t.e,h=e.e,f=f.slice(),s=h-n,s){for(A=s<0,A?(r=f,s=-s,a=C.length):(r=C,n=h,a=f.length),i=Math.max(Math.ceil(R/Q),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=C.length,A=i<a,A&&(a=i),i=0;i<a;i++)if(f[i]!=C[i]){A=f[i]<C[i];break}s=0}for(A&&(r=f,f=C,C=r,t.s=-t.s),a=f.length,i=C.length-a;i>0;--i)f[a++]=0;for(i=C.length;i>s;){if(f[--i]<C[i]){for(o=i;o&&f[--o]===0;)f[o]=te-1;--f[o],f[i]+=te}f[i]-=C[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,W?V(t,R):t):new S(0)}function Ze(e,t,r){var n,i=X(e),o=Re(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ve(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+Ve(-i-1)+o,r&&(n=r-s)>0&&(o+=Ve(n))):i>=s?(o+=Ve(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ve(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ve(n))),e.s<0?"-"+o:o}function jo(e,t){if(e.length>t)return e.length=t,!0}function Jo(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(Ye+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return Bo(s,o.toString())}else if(typeof o!="string")throw Error(Ye+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,Tu.test(o))Bo(s,o);else throw Error(Ye+o)}if(i.prototype=I,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Jo,i.config=i.set=vu,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function vu(e){if(!e||typeof e!="object")throw Error(be+"Object expected");var t,r,n,i=["precision",1,mt,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(dt(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Ye+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Ye+r+": "+n);return this}var mt,Pu,mi,W,be,Ye,pi,dt,ze,Tu,ye,te,Q,Qo,zr,I,Le,mi,Yr,Ko=ge(()=>{"use strict";c();u();p();m();d();l();mt=1e9,Pu={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},W=!0,be="[DecimalError] ",Ye=be+"Invalid argument: ",pi=be+"Exponent out of range: ",dt=Math.floor,ze=Math.pow,Tu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,te=1e7,Q=7,Qo=9007199254740991,zr=dt(Qo/Q),I={};I.absoluteValue=I.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};I.comparedTo=I.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};I.decimalPlaces=I.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*Q;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};I.dividedBy=I.div=function(e){return Le(this,new this.constructor(e))};I.dividedToIntegerBy=I.idiv=function(e){var t=this,r=t.constructor;return V(Le(t,new r(e),0,1),r.precision)};I.equals=I.eq=function(e){return!this.cmp(e)};I.exponent=function(){return X(this)};I.greaterThan=I.gt=function(e){return this.cmp(e)>0};I.greaterThanOrEqualTo=I.gte=function(e){return this.cmp(e)>=0};I.isInteger=I.isint=function(){return this.e>this.d.length-2};I.isNegative=I.isneg=function(){return this.s<0};I.isPositive=I.ispos=function(){return this.s>0};I.isZero=function(){return this.s===0};I.lessThan=I.lt=function(e){return this.cmp(e)<0};I.lessThanOrEqualTo=I.lte=function(e){return this.cmp(e)<1};I.logarithm=I.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ye))throw Error(be+"NaN");if(r.s<1)throw Error(be+(r.s?"NaN":"-Infinity"));return r.eq(ye)?new n(0):(W=!1,t=Le(Gt(r,o),Gt(e,o),o),W=!0,V(t,i))};I.minus=I.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Wo(t,e):Ho(t,(e.s=-e.s,e))};I.modulo=I.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(be+"NaN");return r.s?(W=!1,t=Le(r,e,0,1).times(e),W=!0,r.minus(t)):V(new n(r),i)};I.naturalExponential=I.exp=function(){return Go(this)};I.naturalLogarithm=I.ln=function(){return Gt(this)};I.negated=I.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};I.plus=I.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Ho(t,e):Wo(t,(e.s=-e.s,e))};I.precision=I.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ye+e);if(t=X(i)+1,n=i.d.length-1,r=n*Q+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};I.squareRoot=I.sqrt=function(){var e,t,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(be+"NaN")}for(e=X(a),W=!1,i=Math.sqrt(+a),i==0||i==1/0?(t=Re(a.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=dt((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(Le(a,o,s+2)).times(.5),Re(o.d).slice(0,s)===(t=Re(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if(V(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(t!="9999")break;s+=4}return W=!0,V(n,r)};I.times=I.mul=function(e){var t,r,n,i,o,s,a,f,h,A=this,C=A.constructor,S=A.d,R=(e=new C(e)).d;if(!A.s||!e.s)return new C(0);for(e.s*=A.s,r=A.e+e.e,f=S.length,h=R.length,f<h&&(o=S,S=R,R=o,s=f,f=h,h=s),o=[],s=f+h,n=s;n--;)o.push(0);for(n=h;--n>=0;){for(t=0,i=f+n;i>n;)a=o[i]+R[n]*S[i-n-1]+t,o[i--]=a%te|0,t=a/te|0;o[i]=(o[i]+t)%te|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,W?V(e,C.precision):e};I.toDecimalPlaces=I.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Se(e,0,mt),t===void 0?t=n.rounding:Se(t,0,8),V(r,e+X(r)+1,t))};I.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Ze(n,!0):(Se(e,0,mt),t===void 0?t=i.rounding:Se(t,0,8),n=V(new i(n),e+1,t),r=Ze(n,!0,e+1)),r};I.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?Ze(i):(Se(e,0,mt),t===void 0?t=o.rounding:Se(t,0,8),n=V(new o(i),e+X(i)+1,t),r=Ze(n.abs(),!1,e+X(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};I.toInteger=I.toint=function(){var e=this,t=e.constructor;return V(new t(e),X(e)+1,t.rounding)};I.toNumber=function(){return+this};I.toPower=I.pow=function(e){var t,r,n,i,o,s,a=this,f=a.constructor,h=12,A=+(e=new f(e));if(!e.s)return new f(ye);if(a=new f(a),!a.s){if(e.s<1)throw Error(be+"Infinity");return a}if(a.eq(ye))return a;if(n=f.precision,e.eq(ye))return V(a,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=a.s,s){if((r=A<0?-A:A)<=Qo){for(i=new f(ye),t=Math.ceil(n/Q+4),W=!1;r%2&&(i=i.times(a),jo(i.d,t)),r=dt(r/2),r!==0;)a=a.times(a),jo(a.d,t);return W=!0,e.s<0?new f(ye).div(i):V(i,n)}}else if(o<0)throw Error(be+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,a.s=1,W=!1,i=e.times(Gt(a,n+h)),W=!0,i=Go(i),i.s=o,i};I.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=X(i),n=Ze(i,r<=o.toExpNeg||r>=o.toExpPos)):(Se(e,1,mt),t===void 0?t=o.rounding:Se(t,0,8),i=V(new o(i),e,t),r=X(i),n=Ze(i,e<=r||r<=o.toExpNeg,e)),n};I.toSignificantDigits=I.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Se(e,1,mt),t===void 0?t=n.rounding:Se(t,0,8)),V(new n(r),e,t)};I.toString=I.valueOf=I.val=I.toJSON=I[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=X(e),r=e.constructor;return Ze(e,t<=r.toExpNeg||t>=r.toExpPos)};Le=function(){function e(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%te|0,s=o/te|0;return s&&n.unshift(s),n}function t(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*te+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,h,A,C,S,R,_,k,L,Ee,ue,q,pe,Ke,ai,xe,Gr,Wr=n.constructor,lu=n.s==i.s?1:-1,Ae=n.d,Y=i.d;if(!n.s)return new Wr(n);if(!i.s)throw Error(be+"Division by zero");for(f=n.e-i.e,xe=Y.length,Ke=Ae.length,R=new Wr(lu),_=R.d=[],h=0;Y[h]==(Ae[h]||0);)++h;if(Y[h]>(Ae[h]||0)&&--f,o==null?ue=o=Wr.precision:s?ue=o+(X(n)-X(i))+1:ue=o,ue<0)return new Wr(0);if(ue=ue/Q+2|0,h=0,xe==1)for(A=0,Y=Y[0],ue++;(h<Ke||A)&&ue--;h++)q=A*te+(Ae[h]||0),_[h]=q/Y|0,A=q%Y|0;else{for(A=te/(Y[0]+1)|0,A>1&&(Y=e(Y,A),Ae=e(Ae,A),xe=Y.length,Ke=Ae.length),pe=xe,k=Ae.slice(0,xe),L=k.length;L<xe;)k[L++]=0;Gr=Y.slice(),Gr.unshift(0),ai=Y[0],Y[1]>=te/2&&++ai;do A=0,a=t(Y,k,xe,L),a<0?(Ee=k[0],xe!=L&&(Ee=Ee*te+(k[1]||0)),A=Ee/ai|0,A>1?(A>=te&&(A=te-1),C=e(Y,A),S=C.length,L=k.length,a=t(C,k,S,L),a==1&&(A--,r(C,xe<S?Gr:Y,S))):(A==0&&(a=A=1),C=Y.slice()),S=C.length,S<L&&C.unshift(0),r(k,C,L),a==-1&&(L=k.length,a=t(Y,k,xe,L),a<1&&(A++,r(k,xe<L?Gr:Y,L))),L=k.length):a===0&&(A++,k=[0]),_[h++]=A,a&&k[0]?k[L++]=Ae[pe]||0:(k=[Ae[pe]],L=1);while((pe++<Ke||k[0]!==void 0)&&ue--)}return _[0]||_.shift(),R.e=f,V(R,s?o+X(R)+1:o)}}();mi=Jo(Pu);ye=new mi(1);Yr=mi});var v,ne,l=ge(()=>{"use strict";Ko();v=class extends Yr{static isDecimal(t){return t instanceof Yr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new Yr(`0.${n.slice(0,t)}`)}}},ne=v});function ku(){return!1}function hi(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function Ou(){return hi()}function Du(){return[]}function _u(e){e(null,[])}function Mu(){return""}function Lu(){return""}function Nu(){}function Uu(){}function Fu(){}function $u(){}function Vu(){}function qu(){}function Bu(){}function ju(){}function Qu(){return{close:()=>{},on:()=>{},removeAllListeners:()=>{}}}function Hu(e,t){t(null,hi())}var Gu,Wu,ms,ds=ge(()=>{"use strict";c();u();p();m();d();l();Gu={},Wu={existsSync:ku,lstatSync:hi,stat:Hu,statSync:Ou,readdirSync:Du,readdir:_u,readlinkSync:Mu,realpathSync:Lu,chmodSync:Nu,renameSync:Uu,mkdirSync:Fu,rmdirSync:$u,rmSync:Vu,unlinkSync:qu,watchFile:Bu,unwatchFile:ju,watch:Qu,promises:Gu},ms=Wu});var fs=se(()=>{"use strict";c();u();p();m();d();l()});function Ju(...e){return e.join("/")}function Ku(...e){return e.join("/")}function zu(e){let t=gs(e),r=ys(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function gs(e){let t=e.split("/");return t[t.length-1]}function ys(e){return e.split("/").slice(0,-1).join("/")}function Zu(e){let t=e.split("/").filter(i=>i!==""&&i!=="."),r=[];for(let i of t)i===".."?r.pop():r.push(i);let n=r.join("/");return e.startsWith("/")?"/"+n:n}var hs,Yu,Xu,ep,tn,ws=ge(()=>{"use strict";c();u();p();m();d();l();hs="/",Yu=":";Xu={sep:hs},ep={basename:gs,delimiter:Yu,dirname:ys,join:Ku,normalize:Zu,parse:zu,posix:Xu,resolve:Ju,sep:hs},tn=ep});var bs=se((Vy,tp)=>{tp.exports={name:"@prisma/internals",version:"6.13.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd","@prisma/schema-engine-wasm":"6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var Ei={};pt(Ei,{Hash:()=>Kt,createHash:()=>Es,default:()=>yt,randomFillSync:()=>nn,randomUUID:()=>rn,webcrypto:()=>zt});function rn(){return globalThis.crypto.randomUUID()}function nn(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function Es(e){return new Kt(e)}var zt,Kt,yt,Xe=ge(()=>{"use strict";c();u();p();m();d();l();zt=globalThis.crypto;Kt=class{#e=[];#t;constructor(t){this.#t=t}update(t){this.#e.push(t)}async digest(){let t=new Uint8Array(this.#e.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#e)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#t,t);return new Uint8Array(n)}},yt={webcrypto:zt,randomUUID:rn,randomFillSync:nn,createHash:Es,Hash:Kt}});var xi=se((Dh,op)=>{op.exports={name:"@prisma/engines-version",version:"6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"361e86d0ea4987e9f53a565309b3eed797a6bcbd"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var xs=se(on=>{"use strict";c();u();p();m();d();l();Object.defineProperty(on,"__esModule",{value:!0});on.enginesVersion=void 0;on.enginesVersion=xi().prisma.enginesVersion});var vs=se((Wh,Ts)=>{"use strict";c();u();p();m();d();l();Ts.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Rs=se((aw,Cs)=>{"use strict";c();u();p();m();d();l();Cs.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var vi=se((fw,Ss)=>{"use strict";c();u();p();m();d();l();var up=Rs();Ss.exports=e=>typeof e=="string"?e.replace(up(),""):e});var Is=se((Sw,ln)=>{"use strict";c();u();p();m();d();l();ln.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};ln.exports.default=ln.exports});var Si=se((IP,Ms)=>{"use strict";c();u();p();m();d();l();Ms.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,h,A,C,S,R,_,k,L,Ee,ue,q,pe=[];for(f=0;f<i;f++)pe.push(f+1),pe.push(t.charCodeAt(s+f));for(var Ke=pe.length-1;a<o-3;)for(L=r.charCodeAt(s+(h=a)),Ee=r.charCodeAt(s+(A=a+1)),ue=r.charCodeAt(s+(C=a+2)),q=r.charCodeAt(s+(S=a+3)),R=a+=4,f=0;f<Ke;f+=2)_=pe[f],k=pe[f+1],h=e(_,h,A,L,k),A=e(h,A,C,Ee,k),C=e(A,C,S,ue,k),R=e(C,S,R,q,k),pe[f]=R,S=C,C=A,A=h,h=_;for(;a<o;)for(L=r.charCodeAt(s+(h=a)),R=++a,f=0;f<Ke;f+=2)_=pe[f],pe[f]=R=e(_,h,R,L,pe[f+1]),h=_;return R}}()});var $s=ge(()=>{"use strict";c();u();p();m();d();l()});var Vs=ge(()=>{"use strict";c();u();p();m();d();l()});var Rn,ua=ge(()=>{"use strict";c();u();p();m();d();l();Rn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Zi=se(rt=>{"use strict";c();u();p();m();d();l();Object.defineProperty(rt,"__esModule",{value:!0});rt.anumber=Yi;rt.abytes=sl;rt.ahash=Hm;rt.aexists=Gm;rt.aoutput=Wm;function Yi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function Qm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function sl(e,...t){if(!Qm(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Hm(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Yi(e.outputLen),Yi(e.blockLen)}function Gm(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Wm(e,t){sl(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var Sl=se(D=>{"use strict";c();u();p();m();d();l();Object.defineProperty(D,"__esModule",{value:!0});D.add5L=D.add5H=D.add4H=D.add4L=D.add3H=D.add3L=D.rotlBL=D.rotlBH=D.rotlSL=D.rotlSH=D.rotr32L=D.rotr32H=D.rotrBL=D.rotrBH=D.rotrSL=D.rotrSH=D.shrSL=D.shrSH=D.toBig=void 0;D.fromBig=eo;D.split=al;D.add=xl;var Ln=BigInt(2**32-1),Xi=BigInt(32);function eo(e,t=!1){return t?{h:Number(e&Ln),l:Number(e>>Xi&Ln)}:{h:Number(e>>Xi&Ln)|0,l:Number(e&Ln)|0}}function al(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=eo(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var ll=(e,t)=>BigInt(e>>>0)<<Xi|BigInt(t>>>0);D.toBig=ll;var cl=(e,t,r)=>e>>>r;D.shrSH=cl;var ul=(e,t,r)=>e<<32-r|t>>>r;D.shrSL=ul;var pl=(e,t,r)=>e>>>r|t<<32-r;D.rotrSH=pl;var ml=(e,t,r)=>e<<32-r|t>>>r;D.rotrSL=ml;var dl=(e,t,r)=>e<<64-r|t>>>r-32;D.rotrBH=dl;var fl=(e,t,r)=>e>>>r-32|t<<64-r;D.rotrBL=fl;var gl=(e,t)=>t;D.rotr32H=gl;var yl=(e,t)=>e;D.rotr32L=yl;var hl=(e,t,r)=>e<<r|t>>>32-r;D.rotlSH=hl;var wl=(e,t,r)=>t<<r|e>>>32-r;D.rotlSL=wl;var bl=(e,t,r)=>t<<r-32|e>>>64-r;D.rotlBH=bl;var El=(e,t,r)=>e<<r-32|t>>>64-r;D.rotlBL=El;function xl(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var Pl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);D.add3L=Pl;var Tl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;D.add3H=Tl;var vl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);D.add4L=vl;var Al=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;D.add4H=Al;var Cl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);D.add5L=Cl;var Rl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;D.add5H=Rl;var Jm={fromBig:eo,split:al,toBig:ll,shrSH:cl,shrSL:ul,rotrSH:pl,rotrSL:ml,rotrBH:dl,rotrBL:fl,rotr32H:gl,rotr32L:yl,rotlSH:hl,rotlSL:wl,rotlBH:bl,rotlBL:El,add:xl,add3L:Pl,add3H:Tl,add4L:vl,add4H:Al,add5H:Rl,add5L:Cl};D.default=Jm});var Il=se(Nn=>{"use strict";c();u();p();m();d();l();Object.defineProperty(Nn,"__esModule",{value:!0});Nn.crypto=void 0;var He=(Xe(),$o(Ei));Nn.crypto=He&&typeof He=="object"&&"webcrypto"in He?He.webcrypto:He&&typeof He=="object"&&"randomBytes"in He?He:void 0});var Dl=se(U=>{"use strict";c();u();p();m();d();l();Object.defineProperty(U,"__esModule",{value:!0});U.Hash=U.nextTick=U.byteSwapIfBE=U.isLE=void 0;U.isBytes=Km;U.u8=zm;U.u32=Ym;U.createView=Zm;U.rotr=Xm;U.rotl=ed;U.byteSwap=no;U.byteSwap32=td;U.bytesToHex=nd;U.hexToBytes=id;U.asyncLoop=sd;U.utf8ToBytes=Ol;U.toBytes=Un;U.concatBytes=ad;U.checkOpts=ld;U.wrapConstructor=cd;U.wrapConstructorWithOpts=ud;U.wrapXOFConstructorWithOpts=pd;U.randomBytes=md;var _t=Il(),ro=Zi();function Km(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function zm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Ym(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Zm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Xm(e,t){return e<<32-t|e>>>t}function ed(e,t){return e<<t|e>>>32-t>>>0}U.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function no(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}U.byteSwapIfBE=U.isLE?e=>e:e=>no(e);function td(e){for(let t=0;t<e.length;t++)e[t]=no(e[t])}var rd=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function nd(e){(0,ro.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=rd[e[r]];return t}var Ue={_0:48,_9:57,A:65,F:70,a:97,f:102};function kl(e){if(e>=Ue._0&&e<=Ue._9)return e-Ue._0;if(e>=Ue.A&&e<=Ue.F)return e-(Ue.A-10);if(e>=Ue.a&&e<=Ue.f)return e-(Ue.a-10)}function id(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=kl(e.charCodeAt(o)),a=kl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+a}return n}var od=async()=>{};U.nextTick=od;async function sd(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,U.nextTick)(),n+=o)}}function Ol(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Un(e){return typeof e=="string"&&(e=Ol(e)),(0,ro.abytes)(e),e}function ad(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,ro.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var to=class{clone(){return this._cloneInto()}};U.Hash=to;function ld(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function cd(e){let t=n=>e().update(Un(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function ud(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function pd(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function md(e=32){if(_t.crypto&&typeof _t.crypto.getRandomValues=="function")return _t.crypto.getRandomValues(new Uint8Array(e));if(_t.crypto&&typeof _t.crypto.randomBytes=="function")return _t.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var Vl=se(G=>{"use strict";c();u();p();m();d();l();Object.defineProperty(G,"__esModule",{value:!0});G.shake256=G.shake128=G.keccak_512=G.keccak_384=G.keccak_256=G.keccak_224=G.sha3_512=G.sha3_384=G.sha3_256=G.sha3_224=G.Keccak=void 0;G.keccakP=Fl;var Mt=Zi(),Er=Sl(),Fe=Dl(),Ll=[],Nl=[],Ul=[],dd=BigInt(0),br=BigInt(1),fd=BigInt(2),gd=BigInt(7),yd=BigInt(256),hd=BigInt(113);for(let e=0,t=br,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],Ll.push(2*(5*n+r)),Nl.push((e+1)*(e+2)/2%64);let i=dd;for(let o=0;o<7;o++)t=(t<<br^(t>>gd)*hd)%yd,t&fd&&(i^=br<<(br<<BigInt(o))-br);Ul.push(i)}var[wd,bd]=(0,Er.split)(Ul,!0),_l=(e,t,r)=>r>32?(0,Er.rotlBH)(e,t,r):(0,Er.rotlSH)(e,t,r),Ml=(e,t,r)=>r>32?(0,Er.rotlBL)(e,t,r):(0,Er.rotlSL)(e,t,r);function Fl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,f=(s+2)%10,h=r[f],A=r[f+1],C=_l(h,A,1)^r[a],S=Ml(h,A,1)^r[a+1];for(let R=0;R<50;R+=10)e[s+R]^=C,e[s+R+1]^=S}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Nl[s],f=_l(i,o,a),h=Ml(i,o,a),A=Ll[s];i=e[A],o=e[A+1],e[A]=f,e[A+1]=h}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=wd[n],e[1]^=bd[n]}r.fill(0)}var xr=class e extends Fe.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Mt.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Fe.u32)(this.state)}keccak(){Fe.isLE||(0,Fe.byteSwap32)(this.state32),Fl(this.state32,this.rounds),Fe.isLE||(0,Fe.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Mt.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Fe.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Mt.aexists)(this,!1),(0,Mt.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Mt.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Mt.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};G.Keccak=xr;var Ge=(e,t,r)=>(0,Fe.wrapConstructor)(()=>new xr(t,e,r));G.sha3_224=Ge(6,144,224/8);G.sha3_256=Ge(6,136,256/8);G.sha3_384=Ge(6,104,384/8);G.sha3_512=Ge(6,72,512/8);G.keccak_224=Ge(1,144,224/8);G.keccak_256=Ge(1,136,256/8);G.keccak_384=Ge(1,104,384/8);G.keccak_512=Ge(1,72,512/8);var $l=(e,t,r)=>(0,Fe.wrapXOFConstructorWithOpts)((n={})=>new xr(t,e,n.dkLen===void 0?r:n.dkLen,!0));G.shake128=$l(31,168,128/8);G.shake256=$l(31,136,256/8)});var Jl=se((fN,We)=>{"use strict";c();u();p();m();d();l();var{sha3_512:Ed}=Vl(),Bl=24,Pr=32,io=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function jl(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Ql=(e="")=>jl(Ed(e)).toString(36).slice(1),ql=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),xd=e=>ql[Math.floor(e()*ql.length)],Hl=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+io(Pr,t):io(Pr,t);return Ql(n).substring(0,Pr)},Gl=e=>()=>e++,Pd=476782367,Wl=({random:e=Math.random,counter:t=Gl(Math.floor(e()*Pd)),length:r=Bl,fingerprint:n=Hl({random:e})}={})=>function(){let o=xd(e),s=Date.now().toString(36),a=t().toString(36),f=io(r,e),h=`${s+f+a+n}`;return`${o+Ql(h).substring(1,r)}`},Td=Wl(),vd=(e,{minLength:t=2,maxLength:r=Pr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};We.exports.getConstants=()=>({defaultLength:Bl,bigLength:Pr});We.exports.init=Wl;We.exports.createId=Td;We.exports.bufToBigInt=jl;We.exports.createCounter=Gl;We.exports.createFingerprint=Hl;We.exports.isCuid=vd});var Kl=se((xN,Tr)=>{"use strict";c();u();p();m();d();l();var{createId:Ad,init:Cd,getConstants:Rd,isCuid:Sd}=Jl();Tr.exports.createId=Ad;Tr.exports.init=Cd;Tr.exports.getConstants=Rd;Tr.exports.isCuid=Sd});var Df={};pt(Df,{DMMF:()=>rr,Debug:()=>K,Decimal:()=>ne,Extensions:()=>di,MetricsClient:()=>St,PrismaClientInitializationError:()=>F,PrismaClientKnownRequestError:()=>z,PrismaClientRustPanicError:()=>le,PrismaClientUnknownRequestError:()=>ae,PrismaClientValidationError:()=>ie,Public:()=>fi,Sql:()=>de,createParam:()=>ra,defineDmmfProperty:()=>la,deserializeJsonResponse:()=>qe,deserializeRawResult:()=>oi,dmmfToRuntimeDataModel:()=>_s,empty:()=>ma,getPrismaClient:()=>ou,getRuntime:()=>Dt,join:()=>pa,makeStrictEnum:()=>su,makeTypedQueryFactory:()=>ca,objectEnumValues:()=>hn,raw:()=>Ui,serializeJsonQuery:()=>vn,skip:()=>Tn,sqltag:()=>Fi,warnEnvConflicts:()=>void 0,warnOnce:()=>Xt});module.exports=$o(Df);c();u();p();m();d();l();var di={};pt(di,{defineExtension:()=>zo,getExtensionContext:()=>Yo});c();u();p();m();d();l();c();u();p();m();d();l();function zo(e){return typeof e=="function"?e:t=>t.$extends(e)}c();u();p();m();d();l();function Yo(e){return e}var fi={};pt(fi,{validator:()=>Zo});c();u();p();m();d();l();c();u();p();m();d();l();function Zo(...e){return t=>t}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var gi,Xo,es,ts,rs=!0;typeof g<"u"&&({FORCE_COLOR:gi,NODE_DISABLE_COLORS:Xo,NO_COLOR:es,TERM:ts}=g.env||{},rs=g.stdout&&g.stdout.isTTY);var Au={enabled:!Xo&&es==null&&ts!=="dumb"&&(gi!=null&&gi!=="0"||rs)};function B(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Au.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var kg=B(0,0),Zr=B(1,22),Xr=B(2,22),Og=B(3,23),en=B(4,24),Dg=B(7,27),_g=B(8,28),Mg=B(9,29),Lg=B(30,39),ft=B(31,39),ns=B(32,39),is=B(33,39),os=B(34,39),Ng=B(35,39),ss=B(36,39),Ug=B(37,39),as=B(90,39),Fg=B(90,39),$g=B(40,49),Vg=B(41,49),qg=B(42,49),Bg=B(43,49),jg=B(44,49),Qg=B(45,49),Hg=B(46,49),Gg=B(47,49);c();u();p();m();d();l();var Cu=100,ls=["green","yellow","blue","magenta","cyan","red"],Wt=[],cs=Date.now(),Ru=0,yi=typeof g<"u"?g.env:{};globalThis.DEBUG??=yi.DEBUG??"";globalThis.DEBUG_COLORS??=yi.DEBUG_COLORS?yi.DEBUG_COLORS==="true":!0;var Jt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function Su(e){let t={color:ls[Ru++%ls.length],enabled:Jt.enabled(e),namespace:e,log:Jt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Wt.push([o,...n]),Wt.length>Cu&&Wt.shift(),Jt.enabled(o)||i){let f=n.map(A=>typeof A=="string"?A:Iu(A)),h=`+${Date.now()-cs}ms`;cs=Date.now(),a(o,...f,h)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var K=new Proxy(Su,{get:(e,t)=>Jt[t],set:(e,t,r)=>Jt[t]=r});function Iu(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function us(e=7500){let t=Wt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function ps(){Wt.length=0}c();u();p();m();d();l();c();u();p();m();d();l();var rp=bs(),wi=rp.version;c();u();p();m();d();l();function gt(e){let t=np();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":ip(e))}function np(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function ip(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}c();u();p();m();d();l();c();u();p();m();d();l();function bi(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}c();u();p();m();d();l();var M={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};c();u();p();m();d();l();var Ps="prisma+postgres",sn=`${Ps}:`;function an(e){return e?.toString().startsWith(`${sn}//`)??!1}function Pi(e){if(!an(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var Zt={};pt(Zt,{error:()=>lp,info:()=>ap,log:()=>sp,query:()=>cp,should:()=>As,tags:()=>Yt,warn:()=>Ti});c();u();p();m();d();l();var Yt={error:ft("prisma:error"),warn:is("prisma:warn"),info:ss("prisma:info"),query:os("prisma:query")},As={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function sp(...e){console.log(...e)}function Ti(e,...t){As.warn()&&console.warn(`${Yt.warn} ${e}`,...t)}function ap(e,...t){console.info(`${Yt.info} ${e}`,...t)}function lp(e,...t){console.error(`${Yt.error} ${e}`,...t)}function cp(e,...t){console.log(`${Yt.query} ${e}`,...t)}c();u();p();m();d();l();function Pe(e,t){throw new Error(t)}c();u();p();m();d();l();function Ai(e,t){return Object.prototype.hasOwnProperty.call(e,t)}c();u();p();m();d();l();function ht(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}c();u();p();m();d();l();function Ci(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}c();u();p();m();d();l();function O(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var ks=new Set,Xt=(e,t,...r)=>{ks.has(e)||(ks.add(e),Ti(t,...r))};var F=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};O(F,"PrismaClientInitializationError");c();u();p();m();d();l();var z=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};O(z,"PrismaClientKnownRequestError");c();u();p();m();d();l();var le=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};O(le,"PrismaClientRustPanicError");c();u();p();m();d();l();var ae=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};O(ae,"PrismaClientUnknownRequestError");c();u();p();m();d();l();var ie=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};O(ie,"PrismaClientValidationError");c();u();p();m();d();l();l();function qe(e){return e===null?e:Array.isArray(e)?e.map(qe):typeof e=="object"?pp(e)?mp(e):e.constructor!==null&&e.constructor.name!=="Object"?e:ht(e,qe):e}function pp(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function mp({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new ne(t);case"Json":return JSON.parse(t);default:Pe(t,"Unknown tagged value")}}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Ie=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};c();u();p();m();d();l();function Be(e){return e.substring(0,1).toLowerCase()+e.substring(1)}c();u();p();m();d();l();function Ds(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}c();u();p();m();d();l();function er(e){let t;return{get(){return t||(t={value:e()}),t.value}}}c();u();p();m();d();l();function _s(e){return{models:Ri(e.models),enums:Ri(e.enums),types:Ri(e.types)}}function Ri(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}c();u();p();m();d();l();function wt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function cn(e){return e.toString()!=="Invalid Date"}c();u();p();m();d();l();l();function bt(e){return v.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}c();u();p();m();d();l();c();u();p();m();d();l();var rr={};pt(rr,{ModelAction:()=>tr,datamodelEnumToSchemaEnum:()=>dp});c();u();p();m();d();l();c();u();p();m();d();l();function dp(e){return{name:e.name,values:e.values.map(t=>t.name)}}c();u();p();m();d();l();var tr=(q=>(q.findUnique="findUnique",q.findUniqueOrThrow="findUniqueOrThrow",q.findFirst="findFirst",q.findFirstOrThrow="findFirstOrThrow",q.findMany="findMany",q.create="create",q.createMany="createMany",q.createManyAndReturn="createManyAndReturn",q.update="update",q.updateMany="updateMany",q.updateManyAndReturn="updateManyAndReturn",q.upsert="upsert",q.delete="delete",q.deleteMany="deleteMany",q.groupBy="groupBy",q.count="count",q.aggregate="aggregate",q.findRaw="findRaw",q.aggregateRaw="aggregateRaw",q))(tr||{});var fp=Ce(vs());var gp={red:ft,gray:as,dim:Xr,bold:Zr,underline:en,highlightSource:e=>e.highlight()},yp={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function hp({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function wp({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&a.push(s.underline(bp(t))),i){a.push("");let h=[i.toString()];o&&(h.push(o),h.push(s.dim(")"))),a.push(h.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function bp(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function un(e){let t=e.showColors?gp:yp,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=hp(e),wp(r,t)}c();u();p();m();d();l();var Bs=Ce(Si());c();u();p();m();d();l();function Us(e,t,r){let n=Fs(e),i=Ep(n),o=Pp(i);o?pn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Fs(e){return e.errors.flatMap(t=>t.kind==="Union"?Fs(t):[t])}function Ep(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:xp(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function xp(e,t){return[...new Set(e.concat(t))]}function Pp(e){return Ci(e,(t,r)=>{let n=Ls(t),i=Ls(r);return n!==i?n-i:Ns(t)-Ns(r)})}function Ls(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Ns(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}c();u();p();m();d();l();var he=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};c();u();p();m();d();l();c();u();p();m();d();l();Vs();c();u();p();m();d();l();var Et=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};$s();c();u();p();m();d();l();c();u();p();m();d();l();var mn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};c();u();p();m();d();l();var dn=e=>e,fn={bold:dn,red:dn,green:dn,dim:dn,enabled:!1},qs={bold:Zr,red:ft,green:ns,dim:Xr,enabled:!0},xt={write(e){e.writeLine(",")}};c();u();p();m();d();l();var ke=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};c();u();p();m();d();l();var je=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var Pt=class extends je{items=[];addItem(t){return this.items.push(new mn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new ke("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(xt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Tt=class e extends je{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof Pt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new ke("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(xt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};c();u();p();m();d();l();var re=class extends je{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new ke(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};c();u();p();m();d();l();var nr=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(xt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function pn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":Tp(e,t);break;case"IncludeOnScalar":vp(e,t);break;case"EmptySelection":Ap(e,t,r);break;case"UnknownSelectionField":Ip(e,t);break;case"InvalidSelectionValue":kp(e,t);break;case"UnknownArgument":Op(e,t);break;case"UnknownInputField":Dp(e,t);break;case"RequiredArgumentMissing":_p(e,t);break;case"InvalidArgumentType":Mp(e,t);break;case"InvalidArgumentValue":Lp(e,t);break;case"ValueTooLarge":Np(e,t);break;case"SomeFieldsMissing":Up(e,t);break;case"TooManyFieldsGiven":Fp(e,t);break;case"Union":Us(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function Tp(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function vp(e,t){let[r,n]=vt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new he(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${ir(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function Ap(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){Cp(e,t,i);return}if(n.hasField("select")){Rp(e,t);return}}if(r?.[Be(e.outputType.name)]){Sp(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function Cp(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new he(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Rp(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Hs(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${ir(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Sp(e,t){let r=new nr;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new he("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=vt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new Tt;f.addSuggestion(n),a.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function Ip(e,t){let r=Gs(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Hs(n,e.outputType);break;case"include":$p(n,e.outputType);break;case"omit":Vp(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(ir(n)),i.join(" ")})}function kp(e,t){let r=Gs(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Op(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),qp(n,e.arguments)),t.addErrorMessage(i=>js(i,r,e.arguments.map(o=>o.name)))}function Dp(e,t){let[r,n]=vt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Ws(o,e.inputType)}t.addErrorMessage(o=>js(o,n,e.inputType.fields.map(s=>s.name)))}function js(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=jp(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(ir(e)),n.join(" ")}function _p(e,t){let r;t.addErrorMessage(f=>r?.value instanceof re&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=vt(e.argumentPath),s=new nr,a=n.getDeepFieldValue(i)?.asObject();if(a){if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new he(o,s).makeRequired())}else{let f=e.inputTypes.map(Qs).join(" | ");a.addSuggestion(new he(o,f).makeRequired())}if(e.dependentArgumentPath){n.getDeepField(e.dependentArgumentPath)?.markAsError();let[,f]=vt(e.dependentArgumentPath);t.addErrorMessage(h=>`Argument \`${h.green(o)}\` is required because argument \`${h.green(f)}\` was provided.`)}}}function Qs(e){return e.kind==="list"?`${Qs(e.elementType)}[]`:e.name}function Mp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=gn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Lp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=gn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Np(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof re&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Up(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Ws(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${gn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(ir(i)),o.join(" ")})}function Fp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${gn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Hs(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,"true"))}function $p(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new he(r.name,"true"))}function Vp(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new he(r.name,"true"))}function qp(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function Gs(e,t){let[r,n]=vt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Ws(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function vt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function ir({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function gn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Bp=3;function jp(e,t){let r=1/0,n;for(let i of t){let o=(0,Bs.default)(e,i);o>Bp||o<r&&(r=o,n=i)}return n}c();u();p();m();d();l();c();u();p();m();d();l();var or=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function At(e){return e instanceof or}c();u();p();m();d();l();var yn=Symbol(),ki=new WeakMap,Ne=class{constructor(t){t===yn?ki.set(this,`Prisma.${this._getName()}`):ki.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return ki.get(this)}},sr=class extends Ne{_getNamespace(){return"NullTypes"}},ar=class extends sr{#e};Oi(ar,"DbNull");var lr=class extends sr{#e};Oi(lr,"JsonNull");var cr=class extends sr{#e};Oi(cr,"AnyNull");var hn={classes:{DbNull:ar,JsonNull:lr,AnyNull:cr},instances:{DbNull:new ar(yn),JsonNull:new lr(yn),AnyNull:new cr(yn)}};function Oi(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var Js=": ",wn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Js.length}write(t){let r=new ke(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Js).write(this.value)}};var Di=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Ct(e){return new Di(Ks(e))}function Ks(e){let t=new Tt;for(let[r,n]of Object.entries(e)){let i=new wn(r,zs(n));t.addField(i)}return t}function zs(e){if(typeof e=="string")return new re(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new re(String(e));if(typeof e=="bigint")return new re(`${e}n`);if(e===null)return new re("null");if(e===void 0)return new re("undefined");if(bt(e))return new re(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new re(`Buffer.alloc(${e.byteLength})`):new re(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=cn(e)?e.toISOString():"Invalid Date";return new re(`new Date("${t}")`)}return e instanceof Ne?new re(`Prisma.${e._getName()}`):At(e)?new re(`prisma.${Be(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Qp(e):typeof e=="object"?Ks(e):new re(Object.prototype.toString.call(e))}function Qp(e){let t=new Pt;for(let r of e)t.addItem(zs(r));return t}function bn(e,t){let r=t==="pretty"?qs:fn,n=e.renderAllMessages(r),i=new Et(0,{colors:r}).write(e).toString();return{message:n,args:i}}function En({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=Ct(e);for(let C of t)pn(C,a,s);let{message:f,args:h}=bn(a,r),A=un({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:h});throw new ie(A,{clientVersion:o})}c();u();p();m();d();l();c();u();p();m();d();l();function Oe(e){return e.replace(/^./,t=>t.toLowerCase())}c();u();p();m();d();l();function Zs(e,t,r){let n=Oe(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Hp({...e,...Ys(t.name,e,t.result.$allModels),...Ys(t.name,e,t.result[n])})}function Hp(e){let t=new Ie,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return ht(e,n=>({...n,needs:r(n.name,new Set)}))}function Ys(e,t,r){return r?ht(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Gp(t,o,i)})):{}}function Gp(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Xs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function ea(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var xn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Ie;modelExtensionsCache=new Ie;queryCallbacksCache=new Ie;clientExtensions=er(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=er(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Zs(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Oe(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},Rt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new xn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new xn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};c();u();p();m();d();l();var Pn=class{constructor(t){this.name=t}};function ta(e){return e instanceof Pn}function ra(e){return new Pn(e)}c();u();p();m();d();l();c();u();p();m();d();l();var na=Symbol(),ur=class{constructor(t){if(t!==na)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Tn:t}},Tn=new ur(na);function De(e){return e instanceof ur}var Wp={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},ia="explicitly `undefined` values are not allowed";function vn({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=Rt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:h,globalOmit:A}){let C=new _i({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:h,globalOmit:A});return{modelName:e,action:Wp[t],query:pr(r,C)}}function pr({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:sa(r,n),selection:Jp(e,t,i,n)}}function Jp(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Zp(e,n)):Kp(n,t,r)}function Kp(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&zp(n,t,e),Yp(n,r,e),n}function zp(e,t,r){for(let[n,i]of Object.entries(t)){if(De(i))continue;let o=r.nestSelection(n);if(Mi(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=pr(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=pr(i,o)}}function Yp(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=ea(i,n);for(let[s,a]of Object.entries(o)){if(De(a))continue;Mi(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!a)}}function Zp(e,t){let r={},n=t.getComputedFields(),i=Xs(e,n);for(let[o,s]of Object.entries(i)){if(De(s))continue;let a=t.nestSelection(o);Mi(s,a);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||De(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=pr({},a):r[o]=!0;continue}r[o]=pr(s,a)}}return r}function oa(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(wt(e)){if(cn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(ta(e))return{$type:"Param",value:e.name};if(At(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Xp(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(em(e))return e.values;if(bt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Ne){if(e!==hn.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(tm(e))return e.toJSON();if(typeof e=="object")return sa(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function sa(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);De(i)||(i!==void 0?r[n]=oa(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:ia}))}return r}function Xp(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||De(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(oa(o,i))}return r}function em(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function tm(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Mi(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:ia})}var _i=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){En({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Be(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:Pe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};c();u();p();m();d();l();function aa(e){if(!e._hasPreviewFlag("metrics"))throw new ie("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var St=class{_client;constructor(t){this._client=t}prometheus(t){return aa(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return aa(this._client),this._client._engine.metrics({format:"json",...t})}};c();u();p();m();d();l();function la(e,t){let r=er(()=>rm(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function rm(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function Li(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}c();u();p();m();d();l();var Ni=new WeakMap,An="$$PrismaTypedSql",mr=class{constructor(t,r){Ni.set(this,{sql:t,values:r}),Object.defineProperty(this,An,{value:An})}get sql(){return Ni.get(this).sql}get values(){return Ni.get(this).values}};function ca(e){return(...t)=>new mr(e,t)}function Cn(e){return e!=null&&e[An]===An}c();u();p();m();d();l();var iu=Ce(xi());c();u();p();m();d();l();ua();ds();ws();c();u();p();m();d();l();var de=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function pa(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new de([r,...Array(e.length-1).fill(t),n],e)}function Ui(e){return new de([e],[])}var ma=Ui("");function Fi(e,...t){return new de(e,t)}c();u();p();m();d();l();c();u();p();m();d();l();function dr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}c();u();p();m();d();l();function ce(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}c();u();p();m();d();l();function et(e){let t=new Ie;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}c();u();p();m();d();l();c();u();p();m();d();l();var Sn={enumerable:!0,configurable:!0,writable:!0};function In(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>Sn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var da=Symbol.for("nodejs.util.inspect.custom");function Te(e,t){let r=nm(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=fa(Reflect.ownKeys(o),r),a=fa(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...Sn,...f?.getPropertyDescriptor(s)}:Sn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[da]=function(){let o={...this};return delete o[da],o},i}function nm(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function fa(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}c();u();p();m();d();l();function It(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}c();u();p();m();d();l();function kt(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}c();u();p();m();d();l();function ga(e){if(e===void 0)return"";let t=Ct(e);return new Et(0,{colors:fn}).write(t).toString()}c();u();p();m();d();l();var im="P2037";function kn({error:e,user_facing_error:t},r,n){return t.error_code?new z(om(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new ae(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function om(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===im&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var $i=class{getLocation(){return null}};function Qe(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new $i}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var ya={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Ot(e={}){let t=am(e);return Object.entries(t).reduce((n,[i,o])=>(ya[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function am(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function On(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ha(e,t){let r=On(e);return t({action:"aggregate",unpacker:r,argsMapper:Ot})(e)}c();u();p();m();d();l();function lm(e={}){let{select:t,...r}=e;return typeof t=="object"?Ot({...r,_count:t}):Ot({...r,_count:{_all:!0}})}function cm(e={}){return typeof e.select=="object"?t=>On(e)(t)._count:t=>On(e)(t)._count._all}function wa(e,t){return t({action:"count",unpacker:cm(e),argsMapper:lm})(e)}c();u();p();m();d();l();function um(e={}){let t=Ot(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function pm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function ba(e,t){return t({action:"groupBy",unpacker:pm(e),argsMapper:um})(e)}function Ea(e,t,r){if(t==="aggregate")return n=>ha(n,r);if(t==="count")return n=>wa(n,r);if(t==="groupBy")return n=>ba(n,r)}c();u();p();m();d();l();function xa(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ds(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new or(e,o,s.type,s.isList,s.kind==="enum")},...In(Object.keys(n))})}c();u();p();m();d();l();c();u();p();m();d();l();var Pa=e=>Array.isArray(e)?e:e.split("."),Vi=(e,t)=>Pa(t).reduce((r,n)=>r&&r[n],e),Ta=(e,t,r)=>Pa(t).reduceRight((n,i,o,s)=>Object.assign({},Vi(e,s.slice(0,o)),{[i]:n}),r);function mm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function dm(e,t,r){return t===void 0?e??{}:Ta(t,r,e||!0)}function qi(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((f,h)=>({...f,[h.name]:h}),{});return f=>{let h=Qe(e._errorFormat),A=mm(n,i),C=dm(f,o,A),S=r({dataPath:A,callsite:h})(C),R=fm(e,t);return new Proxy(S,{get(_,k){if(!R.includes(k))return _[k];let Ee=[a[k].type,r,k],ue=[A,C];return qi(e,...Ee,...ue)},...In([...R,...Object.getOwnPropertyNames(S)])})}}function fm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var gm=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],ym=["aggregate","count","groupBy"];function Bi(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[hm(e,t),bm(e,t),dr(r),ce("name",()=>t),ce("$name",()=>t),ce("$parent",()=>e._appliedParent)];return Te({},n)}function hm(e,t){let r=Oe(t),n=Object.keys(tr).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let h=Qe(e._errorFormat);return e._createPrismaPromise(A=>{let C={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:A,callsite:h};return e._request({...C,...a})},{action:o,args:f,model:t})};return gm.includes(o)?qi(e,t,s):wm(i)?Ea(e,i,s):s({})}}}function wm(e){return ym.includes(e)}function bm(e,t){return et(ce("fields",()=>{let r=e._runtimeDataModel.models[t];return xa(t,r)}))}c();u();p();m();d();l();function va(e){return e.replace(/^./,t=>t.toUpperCase())}var ji=Symbol();function fr(e){let t=[Em(e),xm(e),ce(ji,()=>e),ce("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(dr(r)),Te(e,t)}function Em(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function xm(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Oe),n=[...new Set(t.concat(r))];return et({getKeys(){return n},getPropertyValue(i){let o=va(i);if(e._runtimeDataModel.models[o]!==void 0)return Bi(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Bi(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Aa(e){return e[ji]?e[ji]:e}function Ca(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return fr(t)}c();u();p();m();d();l();c();u();p();m();d();l();function Ra({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let h=f.needs.filter(A=>n[A]);h.length>0&&a.push(It(h))}else if(r){if(!r[f.name])continue;let h=f.needs.filter(A=>!r[A]);h.length>0&&a.push(It(h))}Pm(e,f.needs)&&s.push(Tm(f,Te(e,s)))}return s.length>0||a.length>0?Te(e,[...s,...a]):e}function Pm(e,t){return t.every(r=>Ai(e,r))}function Tm(e,t){return et(ce(e.name,()=>e.compute(t)))}c();u();p();m();d();l();function Dn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Dn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Sa({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Sa({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Sa({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||De(s))continue;let f=n.models[r].fields.find(A=>A.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let h=typeof s=="object"?s:{};t[o]=Dn({visitor:i,result:t[o],args:h,modelName:f.type,runtimeDataModel:n})}}function Ia({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Dn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,f,h)=>{let A=Oe(f);return Ra({result:a,modelName:A,select:h.select,omit:h.select?void 0:{...o?.[A],...h.omit},extensions:n})}})}c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();var vm=["$connect","$disconnect","$on","$transaction","$use","$extends"],ka=vm;function Oa(e){if(e instanceof de)return Am(e);if(Cn(e))return Cm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=gr(e[n]);return r}let t={};for(let r in e)t[r]=gr(e[r]);return t}function Am(e){return new de(e.strings,e.values)}function Cm(e){return new mr(e.sql,e.values)}function gr(e){if(typeof e!="object"||e==null||e instanceof Ne||At(e))return e;if(bt(e))return new ne(e.toFixed());if(wt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=gr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:gr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=gr(e[r]);return t}Pe(e,"Unknown value")}function _a(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Oa(t.args??{}),__internalParams:t,query:(s,a=t)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ua(o,f),a.args=s,_a(e,a,r,n+1)}})})}function Ma(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return _a(e,t,s)}function La(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Na(r,n,0,e):e(r)}}function Na(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ua(i,f),Na(a,t,r+1,n)}})}var Da=e=>e;function Ua(e=Da,t=Da){return r=>e(t(r))}c();u();p();m();d();l();var Fa=K("prisma:client"),$a={Vercel:"vercel","Netlify CI":"netlify"};function Va({postinstall:e,ciName:t,clientVersion:r}){if(Fa("checkPlatformCaching:postinstall",e),Fa("checkPlatformCaching:ciName",t),e===!0&&t&&t in $a){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${$a[t]}-build`;throw console.error(n),new F(n,r)}}c();u();p();m();d();l();function qa(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}c();u();p();m();d();l();c();u();p();m();d();l();var Rm=()=>globalThis.process?.release?.name==="node",Sm=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,Im=()=>!!globalThis.Deno,km=()=>typeof globalThis.Netlify=="object",Om=()=>typeof globalThis.EdgeRuntime=="object",Dm=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function _m(){return[[km,"netlify"],[Om,"edge-light"],[Dm,"workerd"],[Im,"deno"],[Sm,"bun"],[Rm,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Mm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Dt(){let e=_m();return{id:e,prettyName:Mm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}c();u();p();m();d();l();c();u();p();m();d();l();var Qi=Ce(vi());c();u();p();m();d();l();function Ba(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}c();u();p();m();d();l();function ja(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}c();u();p();m();d();l();var Qa=Ce(Is());function Ha({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Qa.default)({user:t,repo:r,template:n,title:e,body:i})}function Ga({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=us(6e3-(s?.length??0)),f=ja((0,Qi.default)(a)),h=n?`# Description
\`\`\`
${n}
\`\`\``:"",A=(0,Qi.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${h}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Ba(s):""}
\`\`\`
`),C=Ha({title:r,body:A});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${en(C)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();l();function H(e,t){throw new Error(t)}function Hi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Hi(e[r],t[r]))}function yr(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(ne.isDecimal(e[o])||ne.isDecimal(t[o])){let s=Wa(e[o]),a=Wa(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=Ja(e[o]),a=Ja(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return Ka(e[o])?.getTime()===Ka(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return za(e[o])===za(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return Ya(e[o])===Ya(t[o])}return Hi(e[o],t[o])})}function Wa(e){return ne.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new ne(e):void 0}function Ja(e){return y.isBuffer(e)?e:e instanceof Uint8Array?y.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?y.from(e,"base64"):void 0}function Ka(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function za(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function Ya(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function hr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?y.from(r).toString("base64"):r)}var J=class extends Error{name="DataMapperError"};function el(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new J(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Gi(e,t.fields,r);case"Value":return Wi(e,"<result>",t.resultType,r);default:H(t,`Invalid data mapping type: '${t.type}'`)}}function Gi(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>Za(i,t,r));if(typeof e=="object")return Za(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new J("Expected an array or object, got a string that is not valid JSON",{cause:i})}return Gi(n,t,r)}throw new J(`Expected an array or an object, got: ${typeof e}`)}function Za(e,t,r){if(typeof e!="object")throw new J(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new J(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new J(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=Gi(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=Wi(e[s],s,o.resultType,r);else throw new J(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:H(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function Wi(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new J(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new J(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new J(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new J(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new J(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new J(`Expected a float in column '${t}', got string: ${e}`);return i}throw new J(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new J(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(e instanceof Uint8Array){for(let i of e)if(i!==0)return!0;return!1}throw new J(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!ne.isDecimal(e))throw new J(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Xa(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new J(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Xa(e)}`};throw new J(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>Wi(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:hr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:y.from(e).toString("base64")};throw new J(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new J(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new J(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:H(r,`DataMapper: Unknown result type: ${r.type}`)}}var Lm=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Xa(e){let t=Lm.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var wr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(wr||(wr={}));function Nm(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:H(e,`Unknown provider: ${e}`)}}async function _n({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:wr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":Nm(r)}},async()=>{let o=new Date,s=b.now(),a=await i(),f=b.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),a})}c();u();p();m();d();l();var _e=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function tl(e){if(!bi(e))throw e;let t=Um(e),r=Fm(e);throw!t||!r?e:new _e(r,t,{driverAdapterError:e})}function Um(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseNotReachable":return"P1001";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TlsConnectionError":return"P1011";case"ConnectionClosed":return"P1017";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:H(e.cause,`Unknown error: ${e.cause}`)}}function Fm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseNotReachable":{let t=e.cause.host&&e.cause.port?`${e.cause.host}:${e.cause.port}`:e.cause.host;return`Can't reach database server${t?` at ${t}`:""}`}case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TlsConnectionError":return`Error opening a TLS connection: ${e.cause.reason}`;case"ConnectionClosed":return"Server has closed the connection.";case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Ji(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Ji(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Ji(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:H(e.cause,`Unknown error: ${e.cause}`)}}function Ji(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();function tt(e,t){var r="000000000"+e;return r.substr(r.length-t)}var rl=Ce(fs(),1);function $m(){try{return rl.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var nl=2,Vm=tt(g.pid.toString(36),nl),il=$m(),qm=il.length,Bm=tt(il.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+qm+36).toString(36),nl);function Ki(){return Vm+Bm}c();u();p();m();d();l();c();u();p();m();d();l();function Mn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function zi(e){let n=Math.pow(36,4),i=0;function o(){return tt((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var f="c",h=new Date().getTime().toString(36),A=tt(s().toString(36),4),C=e(),S=o()+o();return f+h+A+C+S}return a.fingerprint=e,a.isCuid=Mn,a}var jm=zi(Ki);var ol=jm;var rc=Ce(Kl());c();u();p();m();d();l();Xe();c();u();p();m();d();l();var zl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var Id=128,nt,Lt;function kd(e){!nt||nt.length<e?(nt=y.allocUnsafe(e*Id),zt.getRandomValues(nt),Lt=0):Lt+e>nt.length&&(zt.getRandomValues(nt),Lt=0),Lt+=e}function oo(e=21){kd(e|=0);let t="";for(let r=Lt-e;r<Lt;r++)t+=zl[nt[r]&63];return t}c();u();p();m();d();l();Xe();var Zl="0123456789ABCDEFGHJKMNPQRSTVWXYZ",vr=32;var Od=16,Xl=10,Yl=0xffffffffffff;var it;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(it||(it={}));var ot=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function Dd(e){let t=Math.floor(e()*vr);return t===vr&&(t=vr-1),Zl.charAt(t)}function _d(e){let t=Md(),r=t&&(t.crypto||t.msCrypto)||(typeof yt<"u"?yt:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(yt?.randomBytes)return()=>yt.randomBytes(1).readUInt8()/255;throw new ot(it.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Md(){return Ud()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function Ld(e,t){let r="";for(;e>0;e--)r=Dd(t)+r;return r}function Nd(e,t=Xl){if(isNaN(e))throw new ot(it.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Yl)throw new ot(it.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Yl}: ${e}`);if(e<0)throw new ot(it.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new ot(it.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%vr,n=Zl.charAt(r)+n,e=(e-r)/vr;return n}function Ud(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function ec(e,t){let r=t||_d(),n=!e||isNaN(e)?Date.now():e;return Nd(n,Xl)+Ld(Od,r)}c();u();p();m();d();l();c();u();p();m();d();l();var oe=[];for(let e=0;e<256;++e)oe.push((e+256).toString(16).slice(1));function Fn(e,t=0){return(oe[e[t+0]]+oe[e[t+1]]+oe[e[t+2]]+oe[e[t+3]]+"-"+oe[e[t+4]]+oe[e[t+5]]+"-"+oe[e[t+6]]+oe[e[t+7]]+"-"+oe[e[t+8]]+oe[e[t+9]]+"-"+oe[e[t+10]]+oe[e[t+11]]+oe[e[t+12]]+oe[e[t+13]]+oe[e[t+14]]+oe[e[t+15]]).toLowerCase()}c();u();p();m();d();l();Xe();var Vn=new Uint8Array(256),$n=Vn.length;function Nt(){return $n>Vn.length-16&&(nn(Vn),$n=0),Vn.slice($n,$n+=16)}c();u();p();m();d();l();c();u();p();m();d();l();Xe();var so={randomUUID:rn};function Fd(e,t,r){if(so.randomUUID&&!t&&!e)return so.randomUUID();e=e||{};let n=e.random??e.rng?.()??Nt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Fn(n)}var ao=Fd;c();u();p();m();d();l();var lo={};function $d(e,t,r){let n;if(e)n=tc(e.random??e.rng?.()??Nt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Nt();Vd(lo,i,o),n=tc(o,lo.msecs,lo.seq,t,r)}return t??Fn(n)}function Vd(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function tc(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var co=$d;var qn=class{#e={};constructor(){this.register("uuid",new mo),this.register("cuid",new fo),this.register("ulid",new go),this.register("nanoid",new yo),this.register("product",new ho)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new po:new uo}})}register(t,r){this.#e[t]=r}},uo=class{#e=new Date;generate(){return this.#e.toISOString()}},po=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},mo=class{generate(t){if(t===4)return ao();if(t===7)return co();throw new Error("Invalid UUID generator arguments")}},fo=class{generate(t){if(t===1)return ol();if(t===2)return(0,rc.createId)();throw new Error("Invalid CUID generator arguments")}},go=class{generate(){return ec()}},yo=class{generate(t){if(typeof t=="number")return oo(t);if(t===void 0)return oo();throw new Error("Invalid Nanoid generator arguments")}},ho=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};c();u();p();m();d();l();c();u();p();m();d();l();function wo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function bo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function nc(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function ic(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function xo(e,t,r){let n=e.type;switch(n){case"rawSql":return sc(e.sql,oc(e.params,t,r));case"templateSql":return qd(e.fragments,e.placeholderFormat,oc(e.params,t,r));default:H(n,"Invalid query type")}}function oc(e,t,r){return e.map(n=>ve(n,t,r))}function ve(e,t,r){let n=e;for(;jd(n);)if(wo(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(bo(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>ve(a,t,r)))}else H(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>ve(i,t,r)):nc(n)?n=y.from(n.prisma__value,"base64"):ic(n)&&(n=BigInt(n.prisma__value)),n}function qd(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let f=a.type;switch(f){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),Eo(t,i++);case"stringChunk":return a.chunk;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let h=r[n++],A=Array.isArray(h)?h:[h];return`(${A.length==0?"NULL":A.map(S=>(o.push(S),Eo(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let h=r[n++];if(!Array.isArray(h))throw new Error("Malformed query template. Tuple list expected.");if(h.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return h.map(C=>{if(!Array.isArray(C))throw new Error("Malformed query template. Tuple expected.");let S=C.map(R=>(o.push(R),Eo(t,i++))).join(a.itemSeparator);return`${a.itemPrefix}${S}${a.itemSuffix}`}).join(a.groupSeparator)}default:H(f,"Invalid fragment type")}}).join("");return sc(s,o)}function Eo(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function sc(e,t){let r=t.map(n=>Bd(n));return{sql:e,args:t,argTypes:r}}function Bd(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":y.isBuffer(e)?"Bytes":"Unknown"}function jd(e){return wo(e)||bo(e)}c();u();p();m();d();l();function lc(e){let t=e.columnTypes.map(r=>{switch(r){case M.Bytes:return n=>Array.isArray(n)?new Uint8Array(n):n;default:return n=>n}});return e.rows.map(r=>r.map((n,i)=>t[i](n)).reduce((n,i,o)=>{let s=e.columnNames[o].split("."),a=n;for(let f=0;f<s.length;f++){let h=s[f];f===s.length-1?a[h]=i:(a[h]===void 0&&(a[h]={}),a=a[h])}return n},{}))}function cc(e){let r=e.columnTypes.map(n=>ac(n)).map(n=>{switch(n){case"bytes":return i=>Array.isArray(i)?new Uint8Array(i):i;case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>ac(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function ac(e){switch(e){case M.Int32:return"int";case M.Int64:return"bigint";case M.Float:return"float";case M.Double:return"double";case M.Text:return"string";case M.Enum:return"enum";case M.Bytes:return"bytes";case M.Boolean:return"bool";case M.Character:return"char";case M.Numeric:return"decimal";case M.Json:return"json";case M.Uuid:return"uuid";case M.DateTime:return"datetime";case M.Date:return"date";case M.Time:return"time";case M.Int32Array:return"int-array";case M.Int64Array:return"bigint-array";case M.FloatArray:return"float-array";case M.DoubleArray:return"double-array";case M.TextArray:return"string-array";case M.EnumArray:return"string-array";case M.BytesArray:return"bytes-array";case M.BooleanArray:return"bool-array";case M.CharacterArray:return"char-array";case M.NumericArray:return"decimal-array";case M.JsonArray:return"json-array";case M.UuidArray:return"uuid-array";case M.DateTimeArray:return"datetime-array";case M.DateArray:return"date-array";case M.TimeArray:return"time-array";case M.UnknownNumber:return"unknown";case M.Set:return"string";default:H(e,`Unexpected column type: ${e}`)}}c();u();p();m();d();l();function uc(e,t,r){if(!t.every(n=>Po(e,n))){let n=Qd(e,r),i=Hd(r);throw new _e(n,i,r.context)}}function Po(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:H(t,`Unknown rule type: ${t.type}`)}}function Qd(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:H(t,`Unknown error identifier: ${t}`)}}function Hd(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:H(e,`Unknown error identifier: ${e}`)}}var Cr=class e{#e;#t;#r;#n=new qn;#o;#i;#s;#a;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a}){this.#e=t,this.#t=r,this.#r=n,this.#o=i,this.#i=o,this.#s=s??o,this.#a=a}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:lc,rawSerializer:cc,provider:t.provider})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#t,this.#n.snapshot(r.provider)).catch(i=>tl(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:ve(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!pc(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(Ar(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>Me(s)+Me(a)):0}}case"execute":{let o=xo(t.args,n,i);return this.#l(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=xo(t.args,n,i);return this.#l(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#s(s),lastInsertId:s.lastInsertId}:{value:this.#i(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(pc(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:dc(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));return{value:Gd(o,a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:el(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return uc(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return Po(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(Ar(s));return{value:Ar(o).filter(f=>!a.has(f))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=new Set,f=[];for(let h of Ar(o)){let A=Bn(h,t.args.fields);a.has(A)||(a.add(A),f.push(h))}return{value:f,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=Ar(o),f=t.args.pagination.linkingFields;if(f!==null){let h=new Map;for(let C of a){let S=Bn(C,f);h.has(S)||h.set(S,[]),h.get(S).push(C)}let A=Array.from(h.entries());return A.sort(([C],[S])=>C<S?-1:C>S?1:0),{value:A.flatMap(([,C])=>mc(C,t.args.pagination)),lastInsertId:s}}return{value:mc(a,t.args.pagination),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,f]of Object.entries(t.args.fields))s[a]=Wd(f,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:To(o);for(let[f,h]of Object.entries(t.args.fields))a[f]=Jd(h,a[f],n,i);return{value:a,lastInsertId:s}}default:H(t,`Unexpected node type: ${t.type}`)}}#l(t,r,n){return _n({query:t,execute:n,provider:this.#a??r.provider,tracingHelper:this.#o,onQuery:this.#r})}};function pc(e){return Array.isArray(e)?e.length===0:e==null}function Ar(e){return Array.isArray(e)?e:[e]}function Me(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function To(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function dc(e,t){return Array.isArray(e)?e.map(r=>dc(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Gd(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let f=To(a),h=Bn(f,i);s[h]||(s[h]=[]),s[h].push(f),r.isRelationUnique?f[r.parentField]=null:f[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let f=Bn(To(a),o);for(let h of s[f]??[])r.isRelationUnique?h[r.parentField]=a:h[r.parentField].push(a)}}return e}function mc(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>yr(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Bn(e,t){return JSON.stringify(t.map(r=>e[r]))}function Wd(e,t,r,n){switch(e.type){case"value":return ve(e.value,r,n);case"lastInsertId":return t;default:H(e,`Unexpected field initializer type: ${e.type}`)}}function Jd(e,t,r,n){switch(e.type){case"set":return ve(e.value,r,n);case"add":return Me(t)+Me(ve(e.value,r,n));case"subtract":return Me(t)-Me(ve(e.value,r,n));case"multiply":return Me(t)*Me(ve(e.value,r,n));case"divide":{let i=Me(t),o=Me(ve(e.value,r,n));return o===0?null:i/o}default:H(e,`Unexpected field operation type: ${e.type}`)}}c();u();p();m();d();l();c();u();p();m();d();l();async function Kd(){return globalThis.crypto??await Promise.resolve().then(()=>(Xe(),Ei))}async function fc(){return(await Kd()).randomUUID()}c();u();p();m();d();l();var we=class extends _e{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Rr=class extends we{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},jn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Qn=class extends we{constructor(t){super(`Transaction is being closed: A ${t} cannot be executed on a closing transaction.`)}},Hn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Gn=class extends we{constructor(){super("Unable to start a transaction in the given time.")}},Wn=class extends we{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},Ut=class extends we{constructor(t){super(`Internal Consistency Error: ${t}`)}},Jn=class extends we{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var zd=100,Sr=K("prisma:client:transactionManager"),Yd=()=>({sql:"COMMIT",args:[],argTypes:[]}),Zd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Xd=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),ef=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Ir=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;#t;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i,this.#t=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await fc(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=!1,o=setTimeout(()=>i=!0,r.maxWait);switch(n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel),clearTimeout(o),n.status){case"waiting":if(i)throw await this.closeTransaction(n,"timed_out"),new Gn;return n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"closing":case"timed_out":case"running":case"committed":case"rolled_back":throw new Ut(`Transaction in invalid state ${n.status} although it just finished startup.`);default:H(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Rr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Sr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"closing":case"waiting":case"running":throw new Ut("Active transaction found in closed transactions list.");case"committed":throw new jn(r);case"rolled_back":throw new Hn(r);case"timed_out":throw new Wn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Sr("Transaction not found.",t),new Rr}if(n.status==="closing")throw new Qn(r);if(["committed","rolled_back","timed_out"].includes(n.status))throw new Ut("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Sr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Sr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){Sr("Closing transaction.",{transactionId:t.id,status:r}),t.status="closing";try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#n(Xd(),t.transaction,()=>t.transaction.commit());else{let n=Yd();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#n(ef(),t.transaction,()=>t.transaction.rollback());else{let n=Zd();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.rollback()}}finally{t.status=r,clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>zd&&this.closedTransactions.shift()}}validateOptions(t){if(!t.timeout)throw new we("timeout is required");if(!t.maxWait)throw new we("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Jn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#n(t,r,n){return _n({query:t,execute:n,provider:this.#t??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Kn="6.13.0";c();u();p();m();d();l();var zn=class e{#e;#t;#r;constructor(t,r,n){this.#e=t,this.#t=r,this.#r=n}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new Ir({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#t.getConnectionInfo?.()??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#t.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#t;return await Cr.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#e.onQuery,tracingHelper:this.#e.tracingHelper,provider:this.#e.provider}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#t.dispose()}}};c();u();p();m();d();l();c();u();p();m();d();l();var Yn=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function gc(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!Yn.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!Yn.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!Yn.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!Yn.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!rf(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function rf(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function yc(e,t){let r=(e||"").split(";").filter(f=>typeof f=="string"&&!!f.trim()),n=r.shift()||"",i=nf(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let f of r){let h=f.split("="),A=(h.shift()||"").trimStart().toLowerCase(),C=h.join("=");switch(A){case"expires":{a.expires=new Date(C);break}case"max-age":{a.maxAge=Number.parseInt(C,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=C;break}default:a[A]=C}}return a}function nf(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}c();u();p();m();d();l();c();u();p();m();d();l();function Ft({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw Dt().id==="workerd"?new F(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new F(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new F("error: Missing URL environment variable, value, or override.",n);return i}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Zn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var fe=class extends Zn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};c();u();p();m();d();l();function N(e,t){return{...e,isRetryable:t}}var st=class extends fe{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,N(r,!1))}};O(st,"InvalidDatasourceError");function Xn(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=Ft({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof g<"u"?g.env:{}}}),i;try{i=new URL(n)}catch{throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==sn)throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new st(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let f=Pi(i)?"http:":"https:",h=new URL(i.href.replace(o,f));return{apiKey:a,url:h}}c();u();p();m();d();l();var hc=Ce(xs()),$t=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":hc.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#e();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#e(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};c();u();p();m();d();l();function of(e){return e[0]*1e3+e[1]/1e6}function Vt(e){return new Date(of(e))}var wc=K("prisma:client:clientEngine:remoteExecutor"),ei=class{#e;#t;#r;#n;#o;constructor(t){this.#e=t.clientVersion,this.#n=t.logEmitter,this.#o=t.tracingHelper;let{url:r,apiKey:n}=Xn({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new vo(r),this.#t=new $t({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#t.build(),body:n,fetch:i});s.ok||await this.#s(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#a(a.extensions),a}async#s(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new z(s,{clientVersion:this.#e,code:n??"P6000",batchRequestIdx:r,meta:a})}#a(t){if(t.logs)for(let r of t.logs)this.#l(r);t.traces&&this.#o.dispatchEngineSpans(t.traces)}#l(t){switch(t.level){case"debug":case"trace":wc(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Vt(t.timestamp),message:t.attributes.message??"",target:t.target});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Vt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},vo=class{#e;#t;#r;constructor(t){this.#e=t,this.#t=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#e),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let f=await o(s,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return wc(t,s,f.status,f.statusText),this.#r=f.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#o(s,f),f}#n(t){let r=[],n=new Date;for(let[i,o]of this.#t){if(o.expires&&o.expires<n){this.#t.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push(gc(o.name,o.value))}return r.length>0?r.join("; "):void 0}#o(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=yc(i),s=o.domain??t.hostname,a=o.path??"/",f=`${s}:${a}:${o.name}`;this.#t.set(f,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};c();u();p();m();d();l();var Ao,bc={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new F("WASM query compiler was unexpectedly `undefined`",t);return Ao===void 0&&(Ao=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new F("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await Ao}};var Ec="P2038",kr=K("prisma:client:clientEngine"),Pc=globalThis;Pc.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new le(e,Kn)}};var Or=class{name="ClientEngine";#e;#t={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#o;constructor(t,r,n){if(!t.previewFeatures?.includes("driverAdapters")&&!r)throw new F("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Ec);if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},kr("Using driver adapter: %O",t.adapter);else throw new F("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Ec);this.#r=n??bc,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#o=i=>{this.logEmitter.emit("query",{...i,params:hr(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#t.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#s(),n=await this.#a(r)}catch(o){throw this.#t={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#t={type:"connected",engine:i},i});return this.#t={type:"connecting",promise:t},await t}case"connecting":return await this.#t.promise;case"connected":return this.#t.engine;case"disconnecting":return await this.#t.promise,await this.#i()}}async#s(){return this.#n.remote?new ei({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await zn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#m(this.config.transactionOptions.isolationLevel)},onQuery:this.#o,provider:this.config.activeProvider})}async#a(t){let r=this.#e;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#e=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#p(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#l(o)}}#l(t){if(t instanceof le)return t;try{let r=JSON.parse(t.message);return new F(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#c(t,r){if(t instanceof F)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new le(xc(this,t.message,r),this.config.clientVersion);if(t instanceof _e)return new z(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new ae(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#u(t){return t instanceof le?t:typeof t.message=="string"&&typeof t.code=="string"?new z(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#p(t,r,n=!0){let i=Pc.PRISMA_WASM_PANIC_REGISTRY.set_message,o;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#e=void 0,n&&this.stop().catch(s=>kr("failed to disconnect:",s)),new le(xc(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#t.type){case"disconnected":return;case"connecting":return await this.#t.promise,await this.stop();case"connected":{let t=this.#t.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#t={type:"disconnected"}}});return this.#t={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#t.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#m(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else Pe(t,"Invalid transaction action.")}catch(s){throw this.#c(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){kr("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(f=>{throw this.#c(f,i)}),a;try{a=this.#p(()=>s.compile(i),i)}catch(f){throw this.#u(f)}try{kr("query plan created",a);let f={},h=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:f,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return kr("query plan executed"),{data:{[t.action]:h}}}catch(f){throw this.#c(f,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(kt(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(h=>{throw this.#c(h,o)}),f;try{f=a.compileBatch(o)}catch(h){throw this.#u(h)}try{let h;r?.kind==="itx"&&(h=r.options);let A={};switch(f.type){case"multi":{if(r?.kind!=="itx"){let R=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;h=await this.transaction("start",{},R)}let C=[],S=!1;for(let[R,_]of f.plans.entries())try{let k=await s.execute({plan:_,placeholderValues:A,model:t[R].modelName,operation:t[R].action,batchIndex:R,transaction:h,customFetch:n?.(globalThis.fetch)});C.push({data:{[t[R].action]:k}})}catch(k){C.push(k),S=!0;break}return h!==void 0&&r?.kind!=="itx"&&(S?await this.transaction("rollback",{},h):await this.transaction("commit",{},h)),C}case"compacted":{if(!t.every(R=>R.action===i))throw new Error("All queries in a batch must have the same action");let C=await s.execute({plan:f.plan,placeholderValues:A,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:h,customFetch:n?.(globalThis.fetch)});return this.#d(C,f,i)}}}catch(h){throw this.#c(h,o)}}metrics(t){throw new Error("Method not implemented.")}#d(t,r,n){let i=t.map(s=>r.keys.reduce((a,f)=>(a[f]=qe(s[f]),a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(f=>yr(f,s));if(a===-1)return r.expectNonEmpty?new z("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let f=Object.entries(t[a]).filter(([h])=>o.has(h));return{data:{[n]:Object.fromEntries(f)}}}})}#m(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new z(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function xc(e,t,r){return Ga({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}c();u();p();m();d();l();c();u();p();m();d();l();var qt=class extends fe{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",N(t,!0))}};O(qt,"ForcedRetryError");c();u();p();m();d();l();var at=class extends fe{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,N(r,!1))}};O(at,"NotImplementedYetError");c();u();p();m();d();l();c();u();p();m();d();l();var j=class extends fe{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var lt=class extends j{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",N(t,!0))}};O(lt,"SchemaMissingError");c();u();p();m();d();l();c();u();p();m();d();l();var Co="This request could not be understood by the server",Dr=class extends j{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||Co,N(t,!1)),n&&(this.code=n)}};O(Dr,"BadRequestError");c();u();p();m();d();l();var _r=class extends j{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",N(t,!0)),this.logs=r}};O(_r,"HealthcheckTimeoutError");c();u();p();m();d();l();var Mr=class extends j{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,N(t,!0)),this.logs=n}};O(Mr,"EngineStartupError");c();u();p();m();d();l();var Lr=class extends j{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",N(t,!1))}};O(Lr,"EngineVersionNotSupportedError");c();u();p();m();d();l();var Ro="Request timed out",Nr=class extends j{name="GatewayTimeoutError";code="P5009";constructor(t,r=Ro){super(r,N(t,!1))}};O(Nr,"GatewayTimeoutError");c();u();p();m();d();l();var sf="Interactive transaction error",Ur=class extends j{name="InteractiveTransactionError";code="P5015";constructor(t,r=sf){super(r,N(t,!1))}};O(Ur,"InteractiveTransactionError");c();u();p();m();d();l();var af="Request parameters are invalid",Fr=class extends j{name="InvalidRequestError";code="P5011";constructor(t,r=af){super(r,N(t,!1))}};O(Fr,"InvalidRequestError");c();u();p();m();d();l();var So="Requested resource does not exist",$r=class extends j{name="NotFoundError";code="P5003";constructor(t,r=So){super(r,N(t,!1))}};O($r,"NotFoundError");c();u();p();m();d();l();var Io="Unknown server error",Bt=class extends j{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||Io,N(t,!0)),this.logs=n}};O(Bt,"ServerError");c();u();p();m();d();l();var ko="Unauthorized, check your connection string",Vr=class extends j{name="UnauthorizedError";code="P5007";constructor(t,r=ko){super(r,N(t,!1))}};O(Vr,"UnauthorizedError");c();u();p();m();d();l();var Oo="Usage exceeded, retry again later",qr=class extends j{name="UsageExceededError";code="P5008";constructor(t,r=Oo){super(r,N(t,!0))}};O(qr,"UsageExceededError");async function lf(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Br(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await lf(e);if(n.type==="QueryEngineError")throw new z(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Bt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new lt(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Lr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Mr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new F(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new _r(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Ur(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Fr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Vr(r,jt(ko,n));if(e.status===404)return new $r(r,jt(So,n));if(e.status===429)throw new qr(r,jt(Oo,n));if(e.status===504)throw new Nr(r,jt(Ro,n));if(e.status>=500)throw new Bt(r,jt(Io,n));if(e.status>=400)throw new Dr(r,jt(Co,n))}function jt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}c();u();p();m();d();l();function Tc(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}c();u();p();m();d();l();var $e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function vc(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,f,h,A;for(let C=0;C<o;C=C+3)A=t[C]<<16|t[C+1]<<8|t[C+2],s=(A&16515072)>>18,a=(A&258048)>>12,f=(A&4032)>>6,h=A&63,r+=$e[s]+$e[a]+$e[f]+$e[h];return i==1?(A=t[o],s=(A&252)>>2,a=(A&3)<<4,r+=$e[s]+$e[a]+"=="):i==2&&(A=t[o]<<8|t[o+1],s=(A&64512)>>10,a=(A&1008)>>4,f=(A&15)<<2,r+=$e[s]+$e[a]+$e[f]+"="),r}c();u();p();m();d();l();function Ac(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new F("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}c();u();p();m();d();l();var Cc={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};c();u();p();m();d();l();c();u();p();m();d();l();var jr=class extends fe{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,N(r,!0))}};O(jr,"RequestError");async function ct(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new jr(a,{clientVersion:n,cause:s})}}var uf=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,Rc=K("prisma:client:dataproxyEngine");async function pf(e,t){let r=Cc["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&uf.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,f,h]=s.split("."),A=mf(`<=${a}.${f}.${h}`),C=await ct(A,{clientVersion:n});if(!C.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${C.status} ${C.statusText}, response body: ${await C.text()||"<empty body>"}`);let S=await C.text();Rc("length of body fetched from unpkg.com",S.length);let R;try{R=JSON.parse(S)}catch(_){throw console.error("JSON.parse error: body fetched from unpkg.com: ",S),_}return R.version}throw new at("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function Sc(e,t){let r=await pf(e,t);return Rc("version",r),r}function mf(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Ic=3,Qr=K("prisma:client:dataproxyEngine"),Hr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){Ac(t),this.config=t,this.env=t.env,this.inlineSchema=vc(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new $t({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await Sc(this.host,this.config),Qr("host",this.host),Qr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Qr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Vt(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Vt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await ct(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Qr("schema response status",r.status);let n=await Br(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=kt(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await ct(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Qr("graphql response status",a.status),await this.handleError(await Br(a,this.clientVersion));let f=await a.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let f=await ct(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Br(f,this.clientVersion));let h=await f.json(),{extensions:A}=h;A&&this.propagateResponseExtensions(A);let C=h.id,S=h["data-proxy"].endpoint;return{id:C,payload:{endpoint:S}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await ct(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Br(a,this.clientVersion));let f=await a.json(),{extensions:h}=f;h&&this.propagateResponseExtensions(h);return}}})}getURLAndAPIKey(){return Xn({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new at("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof fe)||!i.isRetryable)throw i;if(r>=Ic)throw i instanceof qt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Ic} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Tc(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof lt)throw await this.uploadSchema(),new qt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?kn(t[0],this.config.clientVersion,this.config.activeProvider):new ae(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};c();u();p();m();d();l();function kc({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=k=>{i.push({_tag:"warning",value:k})},a=k=>{let L=k.join(`
`);o.push({_tag:"error",value:L})},f=!!e?.startsWith("prisma://"),h=an(e),A=!!t,C=f||h;!A&&r&&C&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let S=C||!r;A&&(S||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?f&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let R={accelerate:S,ppg:h,driverAdapters:A};function _(k){return k.length>0}return _(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:R}:{ok:!0,diagnostics:{warnings:i},isUsing:R}}function Oc({copyEngine:e=!0},t){let r;try{r=Ft({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=kc({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"wasm-compiler-edge"});for(let C of o.warnings)Xt(...C.value);if(!n){let C=o.errors[0];throw new ie(C.value,{clientVersion:t.clientVersion})}let s=gt(t.generator),a=s==="library",f=s==="binary",h=s==="client",A=(i.accelerate||i.ppg)&&!i.driverAdapters;if(h)return new Or(t,A);if(i.accelerate)return new Hr(t);i.driverAdapters,i.accelerate;{let C=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${Dt().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new ie(C.join(`
`),{clientVersion:t.clientVersion})}return"wasm-compiler-edge"}c();u();p();m();d();l();function ti({generator:e}){return e?.previewFeatures??[]}c();u();p();m();d();l();var Dc=e=>({command:e});c();u();p();m();d();l();c();u();p();m();d();l();var _c=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);c();u();p();m();d();l();l();function Qt(e){try{return Mc(e,"fast")}catch{return Mc(e,"slow")}}function Mc(e,t){return JSON.stringify(e.map(r=>Nc(r,t)))}function Nc(e,t){if(Array.isArray(e))return e.map(r=>Nc(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(wt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ne.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(df(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Uc(e):e}function df(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Uc(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Lc);let t={};for(let r of Object.keys(e))t[r]=Lc(e[r]);return t}function Lc(e){return typeof e=="bigint"?e.toString():Uc(e)}var ff=/^(\s*alter\s)/i,Fc=K("prisma:client");function Do(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&ff.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var _o=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Cn(r))n=r.sql,i={values:Qt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Qt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Qt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Qt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=_c(r),i={values:Qt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Fc(`prisma.${e}(${n}, ${i.values})`):Fc(`prisma.${e}(${n})`),{query:n,parameters:i}},$c={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new de(t,r)}},Vc={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};c();u();p();m();d();l();function Mo(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=qc(r(s)):qc(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function qc(e){return typeof e.then=="function"?e:Promise.resolve(e)}c();u();p();m();d();l();var gf=wi.split(".")[0],yf={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Lo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${gf}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??yf}};function Bc(){return new Lo}c();u();p();m();d();l();function jc(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}c();u();p();m();d();l();function Qc(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}c();u();p();m();d();l();var ri=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};c();u();p();m();d();l();var Gc=Ce(vi());c();u();p();m();d();l();function ni(e){return typeof e.batchRequestIdx=="number"}c();u();p();m();d();l();function Hc(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(No(e.query.arguments)),t.push(No(e.query.selection)),t.join("")}function No(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${No(n)})`:r}).join(" ")})`}c();u();p();m();d();l();var hf={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Uo(e){return hf[e]}c();u();p();m();d();l();var ii=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};c();u();p();m();d();l();l();function ut(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ne(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>ut("bigint",r));case"bytes-array":return t.map(r=>ut("bytes",r));case"decimal-array":return t.map(r=>ut("decimal",r));case"datetime-array":return t.map(r=>ut("datetime",r));case"date-array":return t.map(r=>ut("date",r));case"time-array":return t.map(r=>ut("time",r));default:return t}}function oi(e){let t=[],r=wf(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=ut(e.types[s],i[s]);t.push(o)}return t}function wf(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var bf=K("prisma:client:request_handler"),si=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new ii({batchLoader:La(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(C=>C.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),h=n.some(C=>Uo(C.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:Ef(o),containsWrite:h,customDataProxyFetch:i})).map((C,S)=>{if(C instanceof Error)return C;try{return this.mapQueryEngineResult(n[S],C)}catch(R){return R}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Wc(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Uo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Hc(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(bf(t),xf(t,i))throw t;if(t instanceof z&&Pf(t)){let h=Jc(t.meta);En({args:o,errors:[h],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=t.message;if(n&&(f=un({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let h=s?{modelName:s,...t.meta}:t.meta;throw new z(f,{code:t.code,clientVersion:this.client._clientVersion,meta:h,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new le(f,this.client._clientVersion);if(t instanceof ae)throw new ae(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof F)throw new F(f,this.client._clientVersion);if(t instanceof le)throw new le(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Gc.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(h=>h!=="select"&&h!=="include"),a=Vi(o,s),f=i==="queryRaw"?oi(a):qe(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function Ef(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Wc(e)};Pe(e,"Unknown transaction kind")}}function Wc(e){return{id:e.id,payload:e.payload}}function xf(e,t){return ni(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function Pf(e){return e.code==="P2009"||e.code==="P2012"}function Jc(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Jc)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}c();u();p();m();d();l();var Kc=Kn;c();u();p();m();d();l();var eu=Ce(Si());c();u();p();m();d();l();var $=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};O($,"PrismaClientConstructorValidationError");var zc=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Yc=["pretty","colorless","minimal"],Zc=["info","query","warn","error"],Tf={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new $(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Ht(r,t)||` Available datasources: ${t.join(", ")}`;throw new $(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new $(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new $(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new $(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&gt(t.generator)==="client")throw new $('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new $('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!ti(t).includes("driverAdapters"))throw new $('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(gt(t.generator)==="binary")throw new $('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new $(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new $(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Yc.includes(e)){let t=Ht(e,Yc);throw new $(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new $(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Zc.includes(r)){let n=Ht(r,Zc);throw new $(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Ht(i,o);throw new $(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new $(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new $(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new $(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new $('"omit" option is expected to be an object.');if(e===null)throw new $('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Af(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(h=>h.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new $(Cf(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new $(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Ht(r,t);throw new $(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function tu(e,t){for(let[r,n]of Object.entries(e)){if(!zc.includes(r)){let i=Ht(r,zc);throw new $(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Tf[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new $('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Ht(e,t){if(t.length===0||typeof e!="string")return"";let r=vf(e,t);return r?` Did you mean "${r}"?`:""}function vf(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,eu.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Af(e,t){return Xc(t.models,e)??Xc(t.types,e)}function Xc(e,t){let r=Object.keys(e).find(n=>Be(n)===t);if(r)return e[r]}function Cf(e,t){let r=Ct(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=bn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}c();u();p();m();d();l();function ru(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=h=>{o||(o=!0,r(h))};for(let h=0;h<e.length;h++)e[h].then(A=>{n[h]=A,a()},A=>{if(!ni(A)){f(A);return}A.batchRequestIdx===h?f(A):(i||(i=A),a())})})}var Je=K("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Rf={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Sf=Symbol.for("prisma.client.transaction.id"),If={id:0,nextId(){return++this.id}};function ou(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new ri;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Mo();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Va(e),n&&tu(n,e);let i=new Rn().on("error",()=>{});this._extensions=Rt.empty(),this._previewFeatures=ti(e),this._clientVersion=e.clientVersion??Kc,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=Bc();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&tn.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&tn.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==f)throw new F(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new F("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=e.injectableEdgeEnv?.();try{let f=n??{},h=f.__internal??{},A=h.debug===!0;A&&K.enable("prisma:client");let C=tn.resolve(e.dirname,e.relativePath);ms.existsSync(C)||(C=e.dirname),Je("dirname",e.dirname),Je("relativePath",e.relativePath),Je("cwd",C);let S=h.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:C,dirname:e.dirname,enableDebugLogs:A,allowTriggerPanic:S.allowTriggerPanic,prismaPath:S.binaryPath??void 0,engineEndpoint:S.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&Qc(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(R=>typeof R=="string"?R==="query":R.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:qa(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Ft,getBatchRequestPayload:kt,prismaGraphQLToJSError:kn,PrismaClientUnknownRequestError:ae,PrismaClientInitializationError:F,PrismaClientKnownRequestError:z,debug:K("prisma:client:accelerateEngine"),engineVersion:iu.version,clientVersion:e.clientVersion}},Je("clientVersion",e.clientVersion),this._engine=Oc(e,this._engineConfig),this._requestHandler=new si(this,i),f.log)for(let R of f.log){let _=typeof R=="string"?R:R.emit==="stdout"?R.level:null;_&&this.$on(_,k=>{Zt.log(`${Zt.tags[_]??""}`,k.message||k.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=fr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{ps()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:_o({clientMethod:i,activeProvider:a}),callsite:Qe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=nu(n,i);return Do(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new ie("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Do(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new ie(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Dc,callsite:Qe(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:_o({clientMethod:i,activeProvider:a}),callsite:Qe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...nu(n,i));throw new ie("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new ie("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=If.nextId(),s=jc(n.length),a=n.map((f,h)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let A=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,C={kind:"batch",id:o,index:h,isolationLevel:A,lock:s};return f.requestTransaction?.(C)??f});return ru(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let h={kind:"itx",...a};f=await n(this._createItxClient(h)),await this._engine.transaction("commit",o,a)}catch(h){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),h}return f}_createItxClient(n){return Te(fr(Te(Aa(this),[ce("_appliedParent",()=>this._appliedParent._createItxClient(n)),ce("_createPrismaPromise",()=>Mo(n)),ce(Sf,()=>n.id)])),[It(ka)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Rf,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,f=async h=>{let A=this._middlewares.get(++a);if(A)return this._tracingHelper.runInChildSpan(s.middleware,L=>A(h,Ee=>(L?.end(),f(Ee))));let{runInTransaction:C,args:S,...R}=h,_={...n,...R};S&&(_.args=i.middlewareArgsToRequestArgs(S)),n.transaction!==void 0&&C===!1&&delete _.transaction;let k=await Ma(this,_);return _.model?Ia({result:k,modelName:_.model,args:_.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):k};return this._tracingHelper.runInChildSpan(s.operation,()=>f(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:h,transaction:A,unpacker:C,otelParentCtx:S,customDataProxyFetch:R}){try{n=h?h(n):n;let _={name:"serialize"},k=this._tracingHelper.runInChildSpan(_,()=>vn({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return K.enabled("prisma:client")&&(Je("Prisma Client call:"),Je(`prisma.${i}(${ga(n)})`),Je("Generated request:"),Je(JSON.stringify(k,null,2)+`
`)),A?.kind==="batch"&&await A.lock,this._requestHandler.request({protocolQuery:k,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:A,unpacker:C,otelParentCtx:S,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:R})}catch(_){throw _.clientVersion=this._clientVersion,_}}$metrics=new St(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Ca}return t}function nu(e,t){return kf(e)?[new de(e,t),$c]:[e,Vc]}function kf(e){return Array.isArray(e)&&Array.isArray(e.raw)}c();u();p();m();d();l();var Of=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function su(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!Of.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}c();u();p();m();d();l();l();0&&(module.exports={DMMF,Debug,Decimal,Extensions,MetricsClient,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,createParam,defineDmmfProperty,deserializeJsonResponse,deserializeRawResult,dmmfToRuntimeDataModel,empty,getPrismaClient,getRuntime,join,makeStrictEnum,makeTypedQueryFactory,objectEnumValues,raw,serializeJsonQuery,skip,sqltag,warnEnvConflicts,warnOnce});
//# sourceMappingURL=wasm-compiler-edge.js.map
