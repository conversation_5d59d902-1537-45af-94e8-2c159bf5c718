-- ============================================================================
-- NOTIFICATION TRIGGERS FOR REAL-TIME UPDATES
-- ============================================================================

-- Function to send notification events
CREATE OR REPLACE FUNCTION notify_notification_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_data JSON;
    channel_name TEXT;
BEGIN
    -- Determine the channel name based on the operation
    IF TG_OP = 'INSERT' THEN
        channel_name := 'notification_created';
    ELSIF TG_OP = 'UPDATE' THEN
        -- Check if the notification was marked as read
        IF OLD.status != NEW.status AND NEW.status = 'READ' THEN
            channel_name := 'notification_read';
        ELSE
            channel_name := 'notification_updated';
        END IF;
    ELSE
        RETURN NULL;
    END IF;

    -- Build the notification data
    notification_data := json_build_object(
        'id', COALESCE(NEW.id, OLD.id),
        'userId', COALESCE(NEW.userId, OLD.userId),
        'type', COALESCE(NEW.type, OLD.type),
        'title', COALESCE(NEW.title, OLD.title),
        'message', COALESCE(NEW.message, OLD.message),
        'status', COALESCE(NEW.status, OLD.status),
        'priority', COALESCE(NEW.priority, OLD.priority),
        'createdAt', COALESCE(NEW.createdAt, OLD.createdAt),
        'updatedAt', COALESCE(NEW.updatedAt, OLD.updatedAt),
        'readAt', NEW.readAt,
        'data', COALESCE(NEW.data, OLD.data),
        'channels', COALESCE(NEW.channels, OLD.channels),
        'source', COALESCE(NEW.source, OLD.source),
        'referenceId', COALESCE(NEW.referenceId, OLD.referenceId),
        'tags', COALESCE(NEW.tags, OLD.tags)
    );

    -- Send the notification
    PERFORM pg_notify(channel_name, notification_data::text);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for the notifications table
DROP TRIGGER IF EXISTS notification_created_trigger ON "Notification";
DROP TRIGGER IF EXISTS notification_updated_trigger ON "Notification";

CREATE TRIGGER notification_created_trigger
    AFTER INSERT ON "Notification"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_event();

CREATE TRIGGER notification_updated_trigger
    AFTER UPDATE ON "Notification"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_event();

-- ============================================================================
-- NOTIFICATION DELIVERY TRIGGERS
-- ============================================================================

-- Function to send notification delivery events
CREATE OR REPLACE FUNCTION notify_notification_delivery_event()
RETURNS TRIGGER AS $$
DECLARE
    delivery_data JSON;
    channel_name TEXT;
BEGIN
    -- Determine the channel name based on the operation
    IF TG_OP = 'INSERT' THEN
        channel_name := 'notification_delivery_created';
    ELSIF TG_OP = 'UPDATE' THEN
        channel_name := 'notification_delivery_updated';
    ELSE
        RETURN NULL;
    END IF;

    -- Build the delivery data
    delivery_data := json_build_object(
        'id', COALESCE(NEW.id, OLD.id),
        'notificationId', COALESCE(NEW.notificationId, OLD.notificationId),
        'channel', COALESCE(NEW.channel, OLD.channel),
        'status', COALESCE(NEW.status, OLD.status),
        'attempts', COALESCE(NEW.attempts, OLD.attempts),
        'recipientAddress', COALESCE(NEW.recipientAddress, OLD.recipientAddress),
        'sentAt', NEW.sentAt,
        'deliveredAt', NEW.deliveredAt,
        'failedAt', NEW.failedAt,
        'errorMessage', NEW.errorMessage,
        'providerResponse', COALESCE(NEW.providerResponse, OLD.providerResponse)
    );

    -- Send the notification
    PERFORM pg_notify(channel_name, delivery_data::text);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for the notification deliveries table
DROP TRIGGER IF EXISTS notification_delivery_created_trigger ON "NotificationDelivery";
DROP TRIGGER IF EXISTS notification_delivery_updated_trigger ON "NotificationDelivery";

CREATE TRIGGER notification_delivery_created_trigger
    AFTER INSERT ON "NotificationDelivery"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_delivery_event();

CREATE TRIGGER notification_delivery_updated_trigger
    AFTER UPDATE ON "NotificationDelivery"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_delivery_event();

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to manually trigger a notification event (for testing)
CREATE OR REPLACE FUNCTION trigger_test_notification(user_id TEXT)
RETURNS VOID AS $$
DECLARE
    test_data JSON;
BEGIN
    test_data := json_build_object(
        'id', 'test-' || extract(epoch from now()),
        'userId', user_id,
        'type', 'TEST',
        'title', 'Test Notification',
        'message', 'This is a test notification triggered manually',
        'status', 'SENT',
        'priority', 'NORMAL',
        'createdAt', now(),
        'updatedAt', now(),
        'data', json_build_object('test', true),
        'channels', ARRAY['IN_APP'],
        'source', 'MANUAL_TEST'
    );

    PERFORM pg_notify('notification_created', test_data::text);
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes to improve notification queries performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_status 
ON "Notification" (userId, status) 
WHERE status IN ('PENDING', 'SENT', 'DELIVERED');

CREATE INDEX IF NOT EXISTS idx_notifications_created_at 
ON "Notification" (createdAt DESC);

CREATE INDEX IF NOT EXISTS idx_notifications_reference_id 
ON "Notification" (referenceId) 
WHERE referenceId IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_notification_deliveries_notification_id 
ON "NotificationDelivery" (notificationId);

CREATE INDEX IF NOT EXISTS idx_notification_deliveries_status 
ON "NotificationDelivery" (status, channel);

-- ============================================================================
-- COMMENTS
-- ============================================================================

COMMENT ON FUNCTION notify_notification_event() IS 'Trigger function that sends PostgreSQL NOTIFY events when notifications are created or updated';
COMMENT ON FUNCTION notify_notification_delivery_event() IS 'Trigger function that sends PostgreSQL NOTIFY events when notification deliveries are created or updated';
COMMENT ON FUNCTION trigger_test_notification(TEXT) IS 'Utility function to manually trigger a test notification event';

COMMENT ON TRIGGER notification_created_trigger ON "Notification" IS 'Trigger that fires when a new notification is created';
COMMENT ON TRIGGER notification_updated_trigger ON "Notification" IS 'Trigger that fires when a notification is updated';
COMMENT ON TRIGGER notification_delivery_created_trigger ON "NotificationDelivery" IS 'Trigger that fires when a new notification delivery is created';
COMMENT ON TRIGGER notification_delivery_updated_trigger ON "NotificationDelivery" IS 'Trigger that fires when a notification delivery is updated';
