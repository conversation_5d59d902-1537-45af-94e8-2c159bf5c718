import { notificationClient } from './connection';
import logger from '../logger/logger';

const TRIGGER_COMMANDS = [
  // Create the function
  `CREATE OR REPLACE FUNCTION notify_notification_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_data JSON;
    channel_name TEXT;
BEGIN
    -- Determine the channel name based on the operation
    IF TG_OP = 'INSERT' THEN
        channel_name := 'notification_created';
    ELSIF TG_OP = 'UPDATE' THEN
        -- Check if the notification was marked as read
        IF OLD.status != NEW.status AND NEW.status = 'READ' THEN
            channel_name := 'notification_read';
        ELSE
            channel_name := 'notification_updated';
        END IF;
    ELSE
        RETURN NULL;
    END IF;

    -- Build the notification data
    notification_data := json_build_object(
        'id', COALESCE(NEW.id, OLD.id),
        'userId', COALESCE(NEW.userId, OLD.userId),
        'type', COALESCE(NEW.type, OLD.type),
        'title', COALESCE(NEW.title, OLD.title),
        'message', COALESCE(NEW.message, OLD.message),
        'status', COALESCE(NEW.status, OLD.status),
        'priority', COALESCE(NEW.priority, OLD.priority),
        'createdAt', COALESCE(NEW.createdAt, OLD.createdAt),
        'updatedAt', COALESCE(NEW.updatedAt, OLD.updatedAt),
        'readAt', NEW.readAt,
        'data', COALESCE(NEW.data, OLD.data),
        'channels', COALESCE(NEW.channels, OLD.channels),
        'source', COALESCE(NEW.source, OLD.source),
        'referenceId', COALESCE(NEW.referenceId, OLD.referenceId),
        'tags', COALESCE(NEW.tags, OLD.tags)
    );

    -- Send the notification
    PERFORM pg_notify(channel_name, notification_data::text);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql`,

  // Drop existing triggers
  `DROP TRIGGER IF EXISTS notification_created_trigger ON "Notification"`,
  `DROP TRIGGER IF EXISTS notification_updated_trigger ON "Notification"`,

  // Create new triggers
  `CREATE TRIGGER notification_created_trigger
    AFTER INSERT ON "Notification"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_event()`,

  `CREATE TRIGGER notification_updated_trigger
    AFTER UPDATE ON "Notification"
    FOR EACH ROW
    EXECUTE FUNCTION notify_notification_event()`,
];

/**
 * Sets up PostgreSQL triggers for notification events
 */
export async function setupNotificationTriggers(): Promise<void> {
  try {
    logger.info('Setting up PostgreSQL notification triggers...');

    // Execute each command separately
    for (const command of TRIGGER_COMMANDS) {
      await notificationClient.$executeRawUnsafe(command);
    }

    logger.info('✅ PostgreSQL notification triggers set up successfully');
  } catch (error) {
    logger.error('❌ Failed to set up notification triggers', { error });
    throw error;
  }
}

/**
 * Checks if triggers are already set up
 */
export async function checkTriggersExist(): Promise<boolean> {
  try {
    const result = await notificationClient.$queryRaw<
      Array<{ exists: boolean }>
    >`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'notification_created_trigger'
        AND event_object_table = 'Notification'
      ) as exists
    `;

    return result[0]?.exists || false;
  } catch (error) {
    logger.warn('Could not check if triggers exist', { error });
    return false;
  }
}
