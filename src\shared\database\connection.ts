import { PrismaClient as NotificationPrismaClient } from '../../generated/prisma-notification';
import { PrismaClient as BackofficePrismaClient } from '../../generated/prisma-backoffice';
import { env, isDevelopment } from '../config/env';
import logger from '../logger/logger';

// ============================================================================
// NOTIFICATION DATABASE CLIENT
// ============================================================================

const createNotificationClient = () => {
  return new NotificationPrismaClient({
    datasources: {
      db: {
        url: env.NOTIFICATION_DATABASE_URL,
      },
    },
    log: isDevelopment
      ? [
          { emit: 'event', level: 'query' },
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'info' },
          { emit: 'event', level: 'warn' },
        ]
      : [
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'warn' },
        ],
  });
};

// Global para evitar múltiplas instâncias em desenvolvimento
const globalForNotificationClient = globalThis as unknown as {
  notificationClient: NotificationPrismaClient | undefined;
};

export const notificationClient =
  globalForNotificationClient.notificationClient || createNotificationClient();

if (isDevelopment) {
  globalForNotificationClient.notificationClient = notificationClient;
}

// ============================================================================
// BACKOFFICE DATABASE CLIENT (READ-ONLY)
// ============================================================================

const createBackofficeClient = () => {
  return new BackofficePrismaClient({
    datasources: {
      db: {
        url: env.BACKOFFICE_DATABASE_URL,
      },
    },
    log: isDevelopment
      ? [
          { emit: 'event', level: 'query' },
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'info' },
          { emit: 'event', level: 'warn' },
        ]
      : [
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'warn' },
        ],
  });
};

// Global para evitar múltiplas instâncias em desenvolvimento
const globalForBackofficeClient = globalThis as unknown as {
  backofficeClient: BackofficePrismaClient | undefined;
};

export const backofficeClient =
  globalForBackofficeClient.backofficeClient || createBackofficeClient();

if (isDevelopment) {
  globalForBackofficeClient.backofficeClient = backofficeClient;
}

// ============================================================================
// LOGGING SETUP
// ============================================================================

if (isDevelopment) {
  // Logs para o cliente de notificações
  notificationClient.$on('query' as unknown as never, (e: any) => {
    logger.debug('Notification DB Query', {
      query: e.query,
      params: e.params,
      duration: e.duration,
      target: e.target,
    });
  });

  notificationClient.$on('error' as unknown as never, (e: any) => {
    logger.error('Notification DB Error', {
      message: e.message,
      target: e.target,
    });
  });

  // Logs para o cliente de backoffice
  backofficeClient.$on('query' as unknown as never, (e: any) => {
    logger.debug('Backoffice DB Query', {
      query: e.query,
      params: e.params,
      duration: e.duration,
      target: e.target,
    });
  });

  backofficeClient.$on('error' as unknown as never, (e: any) => {
    logger.error('Backoffice DB Error', {
      message: e.message,
      target: e.target,
    });
  });
}

// ============================================================================
// CONNECTION MANAGEMENT
// ============================================================================

/**
 * Conecta ambos os clientes Prisma
 */
export const connectDatabases = async () => {
  try {
    logger.info('Connecting to databases...');

    await Promise.all([
      notificationClient.$connect(),
      backofficeClient.$connect(),
    ]);

    logger.info('Successfully connected to both databases', {
      notification: 'connected',
      backoffice: 'connected',
    });
  } catch (error) {
    logger.error('Failed to connect to databases', { error });
    throw error;
  }
};

/**
 * Desconecta ambos os clientes Prisma
 */
export const disconnectDatabases = async () => {
  try {
    logger.info('Disconnecting from databases...');

    await Promise.all([
      notificationClient.$disconnect(),
      backofficeClient.$disconnect(),
    ]);

    logger.info('Successfully disconnected from both databases');
  } catch (error) {
    logger.error('Failed to disconnect from databases', { error });
    throw error;
  }
};

/**
 * Verifica a saúde das conexões com os bancos
 */
export const checkDatabaseHealth = async () => {
  const health = {
    notification: false,
    backoffice: false,
  };

  try {
    // Testa conexão com banco de notificações
    await notificationClient.$queryRaw`SELECT 1`;
    health.notification = true;
  } catch (error) {
    logger.warn('Notification database health check failed', { error });
  }

  try {
    // Testa conexão com banco de backoffice
    await backofficeClient.$queryRaw`SELECT 1`;
    health.backoffice = true;
  } catch (error) {
    logger.warn('Backoffice database health check failed', { error });
  }

  return health;
};

// ============================================================================
// LEGACY EXPORTS (para compatibilidade)
// ============================================================================

/** @deprecated Use notificationClient instead */
export const prisma = notificationClient;

/** @deprecated Use backofficeClient instead */
export const backofficeDBPrismaClient = backofficeClient;
