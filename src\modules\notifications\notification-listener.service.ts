import { Client } from 'pg';
import { env } from '../../shared/config/env';
import logger from '../../shared/logger/logger';
import { EventEmitter } from 'events';

// ============================================================================
// NOTIFICATION LISTENER SERVICE
// ============================================================================

export interface NotificationEvent {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  status: string;
  createdAt: string;
  data?: any;
}

export class NotificationListenerService extends EventEmitter {
  private pgClient: Client | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 5000; // 5 seconds

  constructor() {
    super();
    this.setMaxListeners(100); // Allow more listeners for multiple SSE connections
  }

  /**
   * Starts the PostgreSQL LISTEN connection
   */
  async start(): Promise<void> {
    if (this.isConnected) {
      logger.warn('Notification listener is already running');
      return;
    }

    logger.info('Starting PostgreSQL notification listener...');

    try {
      await this.connect();
      await this.setupListeners();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      logger.info('PostgreSQL notification listener started successfully');
    } catch (error) {
      logger.error('Failed to start notification listener', { error });
      throw error;
    }
  }

  /**
   * Stops the PostgreSQL LISTEN connection
   */
  async stop(): Promise<void> {
    if (!this.isConnected) {
      logger.warn('Notification listener is not running');
      return;
    }

    logger.info('Stopping PostgreSQL notification listener...');

    try {
      if (this.pgClient) {
        await this.pgClient.end();
        this.pgClient = null;
      }

      this.isConnected = false;
      this.removeAllListeners();

      logger.info('PostgreSQL notification listener stopped');
    } catch (error) {
      logger.error('Error stopping notification listener', { error });
      throw error;
    }
  }

  /**
   * Connects to PostgreSQL database
   */
  private async connect(): Promise<void> {
    this.pgClient = new Client({
      connectionString: env.NOTIFICATION_DATABASE_URL,
      application_name: 'notification-listener'
    });

    this.pgClient.on('error', (error) => {
      logger.error('PostgreSQL client error', { error });
      this.handleConnectionError(error);
    });

    this.pgClient.on('end', () => {
      logger.warn('PostgreSQL connection ended');
      this.isConnected = false;
      this.attemptReconnect();
    });

    await this.pgClient.connect();
    logger.info('Connected to PostgreSQL for notifications');
  }

  /**
   * Sets up LISTEN commands for notification events
   */
  private async setupListeners(): Promise<void> {
    if (!this.pgClient) {
      throw new Error('PostgreSQL client not connected');
    }

    // Listen for notification events
    await this.pgClient.query('LISTEN notification_created');
    await this.pgClient.query('LISTEN notification_updated');
    await this.pgClient.query('LISTEN notification_read');

    // Handle notification messages
    this.pgClient.on('notification', (msg) => {
      this.handleNotification(msg);
    });

    logger.info('PostgreSQL LISTEN commands set up successfully');
  }

  /**
   * Handles incoming PostgreSQL notifications
   */
  private handleNotification(msg: any): void {
    try {
      const { channel, payload } = msg;
      
      if (!payload) {
        logger.warn('Received notification without payload', { channel });
        return;
      }

      const notificationData = JSON.parse(payload) as NotificationEvent;
      
      logger.debug('Received PostgreSQL notification', {
        channel,
        notificationId: notificationData.id,
        userId: notificationData.userId
      });

      // Emit events based on channel
      switch (channel) {
        case 'notification_created':
          this.emit('notification:created', notificationData);
          break;
        case 'notification_updated':
          this.emit('notification:updated', notificationData);
          break;
        case 'notification_read':
          this.emit('notification:read', notificationData);
          break;
        default:
          logger.warn('Unknown notification channel', { channel });
      }

      // Emit a general notification event for all channels
      this.emit('notification', {
        type: channel,
        data: notificationData
      });

    } catch (error) {
      logger.error('Error handling PostgreSQL notification', { error, msg });
    }
  }

  /**
   * Handles connection errors and attempts reconnection
   */
  private handleConnectionError(error: Error): void {
    logger.error('PostgreSQL connection error', { error });
    this.isConnected = false;
    this.attemptReconnect();
  }

  /**
   * Attempts to reconnect to PostgreSQL
   */
  private async attemptReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, giving up');
      this.emit('error', new Error('Failed to reconnect to PostgreSQL after maximum attempts'));
      return;
    }

    this.reconnectAttempts++;
    
    logger.info(`Attempting to reconnect to PostgreSQL (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(async () => {
      try {
        await this.connect();
        await this.setupListeners();
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        logger.info('Successfully reconnected to PostgreSQL');
        this.emit('reconnected');
      } catch (error) {
        logger.error('Reconnection attempt failed', { error });
        this.attemptReconnect();
      }
    }, this.reconnectDelay * this.reconnectAttempts); // Exponential backoff
  }

  /**
   * Gets the current connection status
   */
  getStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }

  /**
   * Manually triggers a test notification (for development/testing)
   */
  async triggerTestNotification(userId: string): Promise<void> {
    if (!this.pgClient || !this.isConnected) {
      throw new Error('PostgreSQL client not connected');
    }

    const testNotification: NotificationEvent = {
      id: 'test-' + Date.now(),
      userId,
      type: 'TEST',
      title: 'Test Notification',
      message: 'This is a test notification from the listener service',
      status: 'SENT',
      createdAt: new Date().toISOString(),
      data: { test: true }
    };

    await this.pgClient.query(
      'NOTIFY notification_created, $1',
      [JSON.stringify(testNotification)]
    );

    logger.info('Test notification triggered', { userId });
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let listenerInstance: NotificationListenerService | null = null;

/**
 * Gets the singleton instance of the notification listener
 */
export const getNotificationListener = (): NotificationListenerService => {
  if (!listenerInstance) {
    listenerInstance = new NotificationListenerService();
  }
  return listenerInstance;
};
