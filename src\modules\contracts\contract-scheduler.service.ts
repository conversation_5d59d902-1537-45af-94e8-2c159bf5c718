import * as cron from 'node-cron';
import { getQueue, QUEUE_NAMES } from '../../shared/queue/connection';
import { ContractReaderJobData, JOB_NAMES } from '../../shared/queue/types';
import { env } from '../../shared/config/env';
import logger from '../../shared/logger/logger';

// ============================================================================
// CONTRACT SCHEDULER SERVICE
// ============================================================================

export class ContractSchedulerService {
  private readonly contractReaderQueue = getQueue(QUEUE_NAMES.CONTRACT_READER);
  private cronTask: cron.ScheduledTask | null = null;
  private isRunning = false;

  /**
   * Starts the CRON scheduler for contract expiration monitoring
   */
  start(): void {
    if (this.cronTask) {
      logger.warn('Contract scheduler is already running');
      return;
    }

    logger.info('Starting contract expiration scheduler', {
      cronPattern: env.CONTRACT_CHECK_CRON
    });

    this.cronTask = cron.schedule(
      env.CONTRACT_CHECK_CRON,
      async () => {
        await this.executeScheduledJob();
      },
      {
        scheduled: false, // Don't start immediately
        timezone: 'America/Sao_Paulo' // Adjust timezone as needed
      }
    );

    this.cronTask.start();
    this.isRunning = true;

    logger.info('Contract expiration scheduler started successfully');
  }

  /**
   * Stops the CRON scheduler
   */
  stop(): void {
    if (!this.cronTask) {
      logger.warn('Contract scheduler is not running');
      return;
    }

    logger.info('Stopping contract expiration scheduler');

    this.cronTask.stop();
    this.cronTask = null;
    this.isRunning = false;

    logger.info('Contract expiration scheduler stopped');
  }

  /**
   * Restarts the scheduler with new configuration
   */
  restart(): void {
    this.stop();
    this.start();
  }

  /**
   * Gets the current status of the scheduler
   */
  getStatus(): {
    isRunning: boolean;
    cronPattern: string;
    nextExecution?: Date;
  } {
    return {
      isRunning: this.isRunning,
      cronPattern: env.CONTRACT_CHECK_CRON,
      nextExecution: this.cronTask ? this.getNextExecutionTime() : undefined
    };
  }

  /**
   * Executes the scheduled contract reading job
   */
  private async executeScheduledJob(): Promise<void> {
    try {
      logger.info('Executing scheduled contract expiration check');

      const jobData: ContractReaderJobData = {
        batchSize: 100,
        daysBeforeExpiration: 30
      };

      await this.contractReaderQueue.add(
        JOB_NAMES.READ_EXPIRING_CONTRACTS,
        jobData,
        {
          priority: 10,
          removeOnComplete: 10,
          removeOnFail: 5
        }
      );

      logger.info('Scheduled contract reading job added to queue successfully');
    } catch (error) {
      logger.error('Error executing scheduled contract reading job', { error });
    }
  }

  /**
   * Gets the next execution time for the CRON job
   */
  private getNextExecutionTime(): Date | undefined {
    if (!this.cronTask) return undefined;

    try {
      // This is a simplified calculation - in production you might want to use a more robust solution
      const now = new Date();
      const cronExpression = env.CONTRACT_CHECK_CRON;
      
      // For the default "0 */6 * * *" (every 6 hours), calculate next execution
      if (cronExpression === '0 */6 * * *') {
        const currentHour = now.getHours();
        const nextHour = Math.ceil((currentHour + 1) / 6) * 6;
        const nextExecution = new Date(now);
        
        if (nextHour >= 24) {
          nextExecution.setDate(nextExecution.getDate() + 1);
          nextExecution.setHours(0, 0, 0, 0);
        } else {
          nextExecution.setHours(nextHour, 0, 0, 0);
        }
        
        return nextExecution;
      }

      // For other patterns, return a generic next hour (this is simplified)
      const nextExecution = new Date(now);
      nextExecution.setHours(nextExecution.getHours() + 1, 0, 0, 0);
      return nextExecution;
    } catch (error) {
      logger.warn('Could not calculate next execution time', { error });
      return undefined;
    }
  }

  /**
   * Manually trigger a contract reading job (for testing or immediate execution)
   */
  async triggerManualExecution(options?: Partial<ContractReaderJobData>): Promise<void> {
    try {
      logger.info('Triggering manual contract expiration check');

      const jobData: ContractReaderJobData = {
        batchSize: 100,
        daysBeforeExpiration: 30,
        ...options
      };

      await this.contractReaderQueue.add(
        JOB_NAMES.READ_EXPIRING_CONTRACTS,
        jobData,
        {
          priority: 1, // Higher priority for manual execution
          removeOnComplete: 5,
          removeOnFail: 3
        }
      );

      logger.info('Manual contract reading job added to queue successfully', { jobData });
    } catch (error) {
      logger.error('Error triggering manual contract reading job', { error });
      throw error;
    }
  }

  /**
   * Gets queue statistics for monitoring
   */
  async getQueueStats() {
    try {
      const queue = this.contractReaderQueue;
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        isPaused: await queue.isPaused()
      };
    } catch (error) {
      logger.error('Error getting queue statistics', { error });
      throw error;
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let schedulerInstance: ContractSchedulerService | null = null;

/**
 * Gets the singleton instance of the contract scheduler
 */
export const getContractScheduler = (): ContractSchedulerService => {
  if (!schedulerInstance) {
    schedulerInstance = new ContractSchedulerService();
  }
  return schedulerInstance;
};
