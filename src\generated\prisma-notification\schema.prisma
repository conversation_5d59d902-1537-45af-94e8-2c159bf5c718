// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma-notification"
}

datasource db {
  provider = "postgresql"
  url      = env("NOTIFICATION_DATABASE_URL")
}

// Enum para tipos de notificação
enum NotificationType {
  SYSTEM_ALERT // Alertas do sistema
  USER_MESSAGE // Mensagens de usuários
  TASK_REMINDER // Lembretes de tarefas
  SECURITY_ALERT // Alertas de segurança
  MARKETING // Notificações de marketing
  TRANSACTION // Notificações de transações
  SOCIAL // Notificações sociais
  NEWS // Notícias e atualizações

  // Contract-related notifications
  CONTRACT_EXPIRING // Contrato vencendo em breve
  CONTRACT_EXPIRED // Contrato vencido
  CONTRACT_RENEWAL // Lembrete de renovação de contrato
  CONTRACT_GENERAL // Notificação geral de contrato
}

// Enum para canais de notificação
enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  IN_APP
  WEBHOOK
  SLACK
  DISCORD
  WHATSAPP
}

// Enum para status da notificação
enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  READ
  FAILED
  CANCELLED
}

// Enum para prioridade da notificação
enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// Model para configuração de notificações do usuário
model UserNotificationConfig {
  id               String              @id @default(cuid())
  userId           String // ID do usuário (referência externa)
  notificationType NotificationType // Tipo de notificação
  channel          NotificationChannel // Canal de notificação
  enabled          Boolean             @default(true) // Se está habilitado

  // Configurações específicas do canal
  emailAddress   String? // Email específico (se diferente do padrão)
  phoneNumber    String? // Número de telefone para SMS
  deviceToken    String? // Token do dispositivo para push
  webhookUrl     String? // URL do webhook
  slackChannel   String? // Canal do Slack
  discordChannel String? // Canal do Discord
  WhatsAppNumber String? // Número do WhatsApp

  // Configurações de horário
  quietHoursStart String? // Início do período silencioso (HH:mm)
  quietHoursEnd   String? // Fim do período silencioso (HH:mm)
  timezone        String? // Fuso horário do usuário

  // Configurações de frequência
  maxFrequency       Int?    @default(0) // Máximo de notificações por hora (0 = ilimitado)
  batchNotifications Boolean @default(false) // Agrupar notificações

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Índices compostos para performance
  @@unique([userId, notificationType, channel])
  @@index([userId])
  @@index([notificationType])
  @@index([channel])
  @@map("user_notification_configs")
}

// Model para as notificações
model Notification {
  id String @id @default(cuid())

  // Identificação
  userId   String // ID do usuário destinatário
  type     NotificationType // Tipo da notificação
  priority NotificationPriority @default(NORMAL)

  // Conteúdo
  title   String // Título da notificação
  message String // Mensagem principal
  data    Json? // Dados adicionais em JSON

  // Configurações de envio
  channels     NotificationChannel[] // Canais para envio
  scheduledFor DateTime? // Agendamento (null = enviar imediatamente)
  expiresAt    DateTime? // Data de expiração

  // Status e controle
  status        NotificationStatus @default(PENDING)
  attempts      Int                @default(0) // Tentativas de envio
  maxAttempts   Int                @default(3) // Máximo de tentativas
  lastAttemptAt DateTime? // Última tentativa de envio
  sentAt        DateTime? // Data/hora do envio
  deliveredAt   DateTime? // Data/hora da entrega
  readAt        DateTime? // Data/hora da leitura

  // Metadados
  source      String? // Origem da notificação
  referenceId String? // ID de referência externa
  tags        String[] // Tags para categorização

  // Relacionamentos
  deliveries NotificationDelivery[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Índices para performance
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([scheduledFor])
  @@index([createdAt])
  @@index([referenceId])
  @@map("notifications")
}

// Model para rastrear entregas por canal
model NotificationDelivery {
  id             String              @id @default(cuid())
  notificationId String // Referência à notificação
  channel        NotificationChannel // Canal usado para entrega

  // Status da entrega
  status   NotificationStatus @default(PENDING)
  attempts Int                @default(0)

  // Detalhes da entrega
  recipientAddress String? // Endereço do destinatário (email, telefone, etc.)
  providerResponse Json? // Resposta do provedor de entrega
  errorMessage     String? // Mensagem de erro, se houver

  // Timestamps
  sentAt      DateTime?
  deliveredAt DateTime?
  failedAt    DateTime?

  // Relacionamento
  notification Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([notificationId])
  @@index([channel])
  @@index([status])
  @@map("notification_deliveries")
}
