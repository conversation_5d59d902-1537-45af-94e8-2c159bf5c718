
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  password: 'password',
  role: 'role',
  keycloakId: 'keycloakId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.UserOtpScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  hash: 'hash',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.SupplierScalarFieldEnum = {
  id: 'id',
  name: 'name',
  document: 'document',
  tradeName: 'tradeName',
  address: 'address',
  classification: 'classification',
  type: 'type',
  status: 'status',
  userId: 'userId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  email: 'email',
  stateRegistration: 'stateRegistration',
  municipalRegistration: 'municipalRegistration',
  taxRegime: 'taxRegime',
  companySize: 'companySize'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  cnpj: 'cnpj',
  address: 'address',
  phone: 'phone',
  email: 'email',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  razaoSocial: 'razaoSocial'
};

exports.Prisma.PaymentMethodScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  label: 'label',
  description: 'description',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.SectorScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  code: 'code',
  description: 'description',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.DomainEventScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  eventName: 'eventName',
  occurredAt: 'occurredAt',
  correlationId: 'correlationId',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.PayablesTypeScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  code: 'code',
  description: 'description',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.EmployeeScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  name: 'name',
  email: 'email',
  position: 'position',
  department: 'department',
  hireDate: 'hireDate',
  address: 'address',
  personalDocuments: 'personalDocuments',
  dependents: 'dependents',
  status: 'status',
  workSchedule: 'workSchedule',
  shift: 'shift',
  grossSalary: 'grossSalary',
  mealAllowance: 'mealAllowance',
  transportAllowance: 'transportAllowance',
  healthPlan: 'healthPlan',
  contractType: 'contractType',
  seniority: 'seniority',
  phone: 'phone',
  birthDate: 'birthDate',
  workHours: 'workHours',
  overtimeBank: 'overtimeBank',
  vacations: 'vacations',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  userId: 'userId'
};

exports.Prisma.ArchiveScalarFieldEnum = {
  id: 'id',
  employeeUuid: 'employeeUuid',
  uploadedBy: 'uploadedBy',
  filePath: 'filePath',
  fileName: 'fileName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  razaoSocial: 'razaoSocial',
  cnpj: 'cnpj',
  email: 'email',
  phone: 'phone',
  address: 'address',
  image: 'image',
  status: 'status',
  userId: 'userId',
  url: 'url',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  parentId: 'parentId'
};

exports.Prisma.CustomerContactScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  contact: 'contact',
  type: 'type',
  area: 'area',
  responsible: 'responsible',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.CostCenterScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  description: 'description',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.PasswordResetTokenScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  expiresAt: 'expiresAt',
  used: 'used',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContractScalarFieldEnum = {
  id: 'id',
  entityType: 'entityType',
  entityUuid: 'entityUuid',
  contractType: 'contractType',
  currentVersion: 'currentVersion',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.ContractVersionScalarFieldEnum = {
  id: 'id',
  versionId: 'versionId',
  uploadedAt: 'uploadedAt',
  uploadedBy: 'uploadedBy',
  filePath: 'filePath',
  signed: 'signed',
  validatedBy: 'validatedBy',
  validatedAt: 'validatedAt',
  startDate: 'startDate',
  expirationDate: 'expirationDate',
  observations: 'observations',
  contractId: 'contractId',
  createdAt: 'createdAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  entityType: 'entityType',
  entityUuid: 'entityUuid',
  uploadedBy: 'uploadedBy',
  currentVersion: 'currentVersion',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  responsible: 'responsible',
  department: 'department',
  description: 'description'
};

exports.Prisma.DocumentVersionScalarFieldEnum = {
  id: 'id',
  versionId: 'versionId',
  uploadedAt: 'uploadedAt',
  uploadedBy: 'uploadedBy',
  expirationDate: 'expirationDate',
  filePath: 'filePath',
  documentId: 'documentId',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerDocumentScalarFieldEnum = {
  id: 'id',
  customerUuid: 'customerUuid',
  name: 'name',
  url: 'url',
  fileName: 'fileName',
  responsible: 'responsible',
  department: 'department',
  description: 'description',
  expirationDate: 'expirationDate',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.LabelScalarFieldEnum = {
  id: 'id',
  label: 'label',
  idComponent: 'idComponent',
  modulo: 'modulo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerPaymentPreferenceScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  department: 'department',
  responsible: 'responsible',
  paymentMethodId: 'paymentMethodId',
  paymentCondition: 'paymentCondition',
  billingPreference: 'billingPreference',
  costCenterId: 'costCenterId',
  sectorId: 'sectorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupplierContactScalarFieldEnum = {
  id: 'id',
  supplierId: 'supplierId',
  contact: 'contact',
  type: 'type',
  area: 'area',
  responsible: 'responsible',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  entityType: 'entityType',
  entityUuid: 'entityUuid',
  type: 'type',
  rate: 'rate',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt'
};

exports.Prisma.DomainScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  customerUuid: 'customerUuid',
  domain: 'domain',
  licenseNumber: 'licenseNumber',
  licenseType: 'licenseType',
  brandName: 'brandName',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.BankDataScalarFieldEnum = {
  id: 'id',
  entityType: 'entityType',
  entityUuid: 'entityUuid',
  bankName: 'bankName',
  bankCode: 'bankCode',
  accountType: 'accountType',
  agencyNumber: 'agencyNumber',
  agencyDigit: 'agencyDigit',
  accountNumber: 'accountNumber',
  accountDigit: 'accountDigit',
  accountHolderName: 'accountHolderName',
  accountHolderDocument: 'accountHolderDocument',
  pixKey: 'pixKey',
  pixKeyType: 'pixKeyType',
  isDigitalBank: 'isDigitalBank',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BankAccountUpsertDataScalarFieldEnum = {
  id: 'id',
  bankDataId: 'bankDataId',
  fieldName: 'fieldName',
  oldValue: 'oldValue',
  newValue: 'newValue',
  updatedAt: 'updatedAt',
  entityType: 'entityType',
  entityUuid: 'entityUuid'
};

exports.Prisma.CertificateScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  category: 'category',
  type: 'type',
  fileUrl: 'fileUrl',
  notes: 'notes',
  emissionDate: 'emissionDate',
  expirationDate: 'expirationDate',
  uploadedById: 'uploadedById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  FINANCE_ADMIN: 'FINANCE_ADMIN',
  FINANCE_USER: 'FINANCE_USER',
  DOCUMENT_ARCHIVER: 'DOCUMENT_ARCHIVER',
  DOCUMENT_UPLOADER: 'DOCUMENT_UPLOADER',
  DOCUMENT_VIEWER: 'DOCUMENT_VIEWER',
  DOCUMENT_DOWNLOADER: 'DOCUMENT_DOWNLOADER',
  SUPPLIER_VIEWER: 'SUPPLIER_VIEWER',
  CUSTOMER_VIEWER: 'CUSTOMER_VIEWER',
  EMPLOYEE: 'EMPLOYEE'
};

exports.SupplierClassification = exports.$Enums.SupplierClassification = {
  CORE: 'CORE',
  GENERAL: 'GENERAL'
};

exports.SupplierType = exports.$Enums.SupplierType = {
  BANK: 'BANK',
  GAME: 'GAME',
  SPORTSBOOK: 'SPORTSBOOK',
  KYC: 'KYC',
  OTHER: 'OTHER'
};

exports.SupplierStatus = exports.$Enums.SupplierStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING'
};

exports.TaxRegime = exports.$Enums.TaxRegime = {
  SIMPLES_NACIONAL: 'SIMPLES_NACIONAL',
  LUCRO_PRESUMIDO: 'LUCRO_PRESUMIDO',
  LUCRO_REAL: 'LUCRO_REAL',
  MEI: 'MEI'
};

exports.CompanySize = exports.$Enums.CompanySize = {
  MEI: 'MEI',
  MICROEMPRESA: 'MICROEMPRESA',
  PEQUENO_PORTE: 'PEQUENO_PORTE',
  MEDIO_PORTE: 'MEDIO_PORTE',
  GRANDE_PORTE: 'GRANDE_PORTE'
};

exports.CompanyStatus = exports.$Enums.CompanyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.EmployeeStatus = exports.$Enums.EmployeeStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.CustomerStatus = exports.$Enums.CustomerStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING'
};

exports.EntityType = exports.$Enums.EntityType = {
  CLIENT: 'CLIENT',
  COLLABORATE: 'COLLABORATE',
  SUPPLIER: 'SUPPLIER'
};

exports.ContractType = exports.$Enums.ContractType = {
  CERT_GAME: 'CERT_GAME',
  CERT_RNG: 'CERT_RNG',
  CERT_RGS: 'CERT_RGS',
  CERT_PLATFORM: 'CERT_PLATFORM',
  CERT_INTEGRATION: 'CERT_INTEGRATION',
  CERT_KYC: 'CERT_KYC',
  CERT_PAYMENT: 'CERT_PAYMENT',
  CERT_SPORTSBOOK: 'CERT_SPORTSBOOK'
};

exports.ContractStatus = exports.$Enums.ContractStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.DocumentStatus = exports.$Enums.DocumentStatus = {
  ACTIVE: 'ACTIVE',
  ARCHIVED: 'ARCHIVED'
};

exports.LicenseType = exports.$Enums.LicenseType = {
  FEDERAL: 'FEDERAL',
  ESTADUAL: 'ESTADUAL'
};

exports.BankAccountType = exports.$Enums.BankAccountType = {
  CHECKING: 'CHECKING',
  SAVINGS: 'SAVINGS',
  SALARY: 'SALARY',
  BUSINESS: 'BUSINESS'
};

exports.BankPixKeyType = exports.$Enums.BankPixKeyType = {
  CPF: 'CPF',
  CNPJ: 'CNPJ',
  EMAIL: 'EMAIL',
  PHONE: 'PHONE',
  RANDOM: 'RANDOM'
};

exports.BankDataStatus = exports.$Enums.BankDataStatus = {
  ACTIVE: 'ACTIVE',
  AWAITING_APPROVAL: 'AWAITING_APPROVAL',
  REJECTED: 'REJECTED',
  INACTIVE: 'INACTIVE'
};

exports.CertificateCategory = exports.$Enums.CertificateCategory = {
  FORNECEDORES_JOGOS: 'FORNECEDORES_JOGOS',
  FORNECEDORES_KYC: 'FORNECEDORES_KYC',
  PAGAMENTOS: 'PAGAMENTOS',
  SPORTSBOOK: 'SPORTSBOOK'
};

exports.CertificateType = exports.$Enums.CertificateType = {
  CERTIFICADO_DE_JOGO: 'CERTIFICADO_DE_JOGO',
  CERTIFICADO_RNG: 'CERTIFICADO_RNG',
  CERTIFICADO_RGS: 'CERTIFICADO_RGS',
  CERTIFICADO_DE_PLATAFORMA: 'CERTIFICADO_DE_PLATAFORMA',
  CERTIFICADO_DE_INTEGRACAO: 'CERTIFICADO_DE_INTEGRACAO',
  CERTIFICADO_DE_KYC: 'CERTIFICADO_DE_KYC',
  CERTIFICADO_DE_MEIOS_DE_PAGAMENTO: 'CERTIFICADO_DE_MEIOS_DE_PAGAMENTO',
  CERTIFICADO_DE_SPORTSBOOK: 'CERTIFICADO_DE_SPORTSBOOK'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserOtp: 'UserOtp',
  Supplier: 'Supplier',
  Company: 'Company',
  PaymentMethod: 'PaymentMethod',
  Sector: 'Sector',
  DomainEvent: 'DomainEvent',
  PayablesType: 'PayablesType',
  Employee: 'Employee',
  Archive: 'Archive',
  Customer: 'Customer',
  CustomerContact: 'CustomerContact',
  CostCenter: 'CostCenter',
  PasswordResetToken: 'PasswordResetToken',
  Contract: 'Contract',
  ContractVersion: 'ContractVersion',
  Document: 'Document',
  DocumentVersion: 'DocumentVersion',
  CustomerDocument: 'CustomerDocument',
  Label: 'Label',
  CustomerPaymentPreference: 'CustomerPaymentPreference',
  SupplierContact: 'SupplierContact',
  Service: 'Service',
  Domain: 'Domain',
  BankData: 'BankData',
  BankAccountUpsertData: 'BankAccountUpsertData',
  Certificate: 'Certificate'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
