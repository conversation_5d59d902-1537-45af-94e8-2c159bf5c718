import Fastify from 'fastify';
import { env, isDevelopment } from './shared/config/env';
import logger from './shared/logger/logger';
import { securityMiddleware } from './shared/middleware/security.middleware';
import { errorHandler } from './shared/middleware/error-handler.middleware';
import { getAppService } from './modules/app.service';
import { NotificationController } from './modules/notifications/notification.controller';

const fastify = Fastify({
  logger: isDevelopment
    ? {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
          },
        },
        serializers: {
          req(request) {
            // Não logar requisições para endpoints de monitoramento
            if (
              request.url === '/metrics' ||
              request.url?.startsWith('/health')
            ) {
              return {};
            }
            return {
              method: request.method,
              url: request.url,
              hostname: request.hostname,
              remoteAddress: request.ip,
              remotePort: request.socket?.remotePort,
            };
          },
        },
      }
    : {
        serializers: {
          req(request) {
            // Não logar requisições para endpoints de monitoramento em produção também
            if (
              request.url === '/metrics' ||
              request.url?.startsWith('/health')
            ) {
              return {};
            }
            return {
              method: request.method,
              url: request.url,
              hostname: request.hostname,
              remoteAddress: request.ip,
            };
          },
        },
      },
});

// Adicionar middleware de segurança
fastify.addHook(
  'preHandler',
  securityMiddleware({
    enableSqlInjectionProtection: true,
    enableXssProtection: true,
    enablePathTraversalProtection: true,
    enableInputSanitization: true,
    strictMode: false,
    logAttempts: true,
  })
);

// Configurar o manipulador de erros
fastify.setErrorHandler(errorHandler);

const setupRoutes = async () => {
  if (env.CORS_ENABLED) {
    await fastify.register(import('@fastify/cors'), {
      origin: isDevelopment ? true : false,
    });
  }

  // Register notification routes
  const notificationController = new NotificationController();
  await notificationController.registerRoutes(fastify);

  // Health check endpoint
  fastify.get('/health', async (request, reply) => {
    const appService = getAppService();
    const healthCheck = await appService.healthCheck();

    reply.code(healthCheck.status === 'healthy' ? 200 : 503).send(healthCheck);
  });

  // Status endpoint
  fastify.get('/status', async (request, reply) => {
    const appService = getAppService();
    const status = appService.getStatus();

    reply.send(status);
  });

  // Queue statistics endpoint (for monitoring)
  fastify.get('/admin/queue-stats', async (request, reply) => {
    try {
      const appService = getAppService();
      const stats = await appService.getQueueStatistics();
      reply.send(stats);
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get queue statistics' });
    }
  });

  // Manual contract reading trigger (for admin/testing)
  fastify.post('/admin/trigger-contract-reading', async (request, reply) => {
    try {
      const appService = getAppService();
      await appService.triggerContractReading(request.body);
      reply.send({ success: true, message: 'Contract reading triggered' });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to trigger contract reading' });
    }
  });
};

const start = async () => {
  try {
    // Initialize application services
    logger.info('Starting Cactus Notification Service...');
    const appService = getAppService();
    await appService.initialize();

    // Setup routes
    await setupRoutes();

    // Start the server
    await fastify.listen({ port: env.PORT, host: '0.0.0.0' });

    logger.info(`🚀 Server listening on port ${env.PORT}`);
    logger.info(`Environment: ${env.NODE_ENV}`);
    logger.info(`Debug mode: ${env.DEBUG}`);
  } catch (error) {
    logger.error('Error starting server', { error });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);

  try {
    const appService = getAppService();
    await appService.shutdown();
    await fastify.close();
    logger.info('Server shut down successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown', { error });
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the application
start();
