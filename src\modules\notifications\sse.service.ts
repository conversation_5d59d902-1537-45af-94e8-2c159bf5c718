import { FastifyRequest, FastifyReply } from 'fastify';
import { getNotificationListener, NotificationEvent } from './notification-listener.service';
import logger from '../../shared/logger/logger';

// ============================================================================
// SSE CONNECTION MANAGEMENT
// ============================================================================

interface SSEConnection {
  id: string;
  userId: string;
  reply: FastifyReply;
  lastEventId?: string;
  isActive: boolean;
  connectedAt: Date;
}

export class SSEService {
  private connections = new Map<string, SSEConnection>();
  private userConnections = new Map<string, Set<string>>();
  private notificationListener = getNotificationListener();

  constructor() {
    this.setupNotificationListeners();
  }

  /**
   * Handles new SSE connection
   */
  async handleConnection(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const userId = this.extractUserId(request);
    const connectionId = this.generateConnectionId();
    const lastEventId = request.headers['last-event-id'] as string;

    if (!userId) {
      reply.code(401).send({ error: 'Unauthorized' });
      return;
    }

    logger.info('New SSE connection', { userId, connectionId });

    // Set SSE headers
    reply.raw.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    });

    // Create connection object
    const connection: SSEConnection = {
      id: connectionId,
      userId,
      reply,
      lastEventId,
      isActive: true,
      connectedAt: new Date()
    };

    // Store connection
    this.connections.set(connectionId, connection);
    
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(connectionId);

    // Send initial connection event
    this.sendEvent(connectionId, {
      type: 'connected',
      data: {
        connectionId,
        timestamp: new Date().toISOString()
      }
    });

    // Send missed notifications if lastEventId is provided
    if (lastEventId) {
      await this.sendMissedNotifications(connectionId, lastEventId);
    }

    // Handle connection close
    request.raw.on('close', () => {
      this.handleDisconnection(connectionId);
    });

    request.raw.on('error', (error) => {
      logger.error('SSE connection error', { error, userId, connectionId });
      this.handleDisconnection(connectionId);
    });

    // Keep connection alive with periodic heartbeat
    this.startHeartbeat(connectionId);
  }

  /**
   * Sets up listeners for notification events
   */
  private setupNotificationListeners(): void {
    this.notificationListener.on('notification:created', (notification: NotificationEvent) => {
      this.broadcastToUser(notification.userId, {
        type: 'notification:created',
        data: notification
      });
    });

    this.notificationListener.on('notification:updated', (notification: NotificationEvent) => {
      this.broadcastToUser(notification.userId, {
        type: 'notification:updated',
        data: notification
      });
    });

    this.notificationListener.on('notification:read', (notification: NotificationEvent) => {
      this.broadcastToUser(notification.userId, {
        type: 'notification:read',
        data: notification
      });
    });
  }

  /**
   * Broadcasts an event to all connections for a specific user
   */
  private broadcastToUser(userId: string, event: any): void {
    const userConnectionIds = this.userConnections.get(userId);
    
    if (!userConnectionIds || userConnectionIds.size === 0) {
      return;
    }

    logger.debug('Broadcasting to user', { 
      userId, 
      connectionCount: userConnectionIds.size,
      eventType: event.type 
    });

    userConnectionIds.forEach(connectionId => {
      this.sendEvent(connectionId, event);
    });
  }

  /**
   * Sends an event to a specific connection
   */
  private sendEvent(connectionId: string, event: any): void {
    const connection = this.connections.get(connectionId);
    
    if (!connection || !connection.isActive) {
      return;
    }

    try {
      const eventId = this.generateEventId();
      const eventData = JSON.stringify(event.data || {});
      
      const sseMessage = [
        `id: ${eventId}`,
        `event: ${event.type}`,
        `data: ${eventData}`,
        '', // Empty line to end the event
      ].join('\n');

      connection.reply.raw.write(sseMessage + '\n');

      logger.debug('SSE event sent', {
        connectionId,
        userId: connection.userId,
        eventType: event.type,
        eventId
      });

    } catch (error) {
      logger.error('Error sending SSE event', {
        error,
        connectionId,
        userId: connection.userId
      });
      
      this.handleDisconnection(connectionId);
    }
  }

  /**
   * Handles connection disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    
    if (!connection) {
      return;
    }

    logger.info('SSE connection closed', {
      connectionId,
      userId: connection.userId,
      duration: Date.now() - connection.connectedAt.getTime()
    });

    // Mark as inactive
    connection.isActive = false;

    // Remove from user connections
    const userConnectionIds = this.userConnections.get(connection.userId);
    if (userConnectionIds) {
      userConnectionIds.delete(connectionId);
      
      if (userConnectionIds.size === 0) {
        this.userConnections.delete(connection.userId);
      }
    }

    // Remove connection
    this.connections.delete(connectionId);

    // Close the response if still open
    try {
      if (!connection.reply.raw.destroyed) {
        connection.reply.raw.end();
      }
    } catch (error) {
      logger.debug('Error closing SSE connection', { error, connectionId });
    }
  }

  /**
   * Starts heartbeat for a connection to keep it alive
   */
  private startHeartbeat(connectionId: string): void {
    const heartbeatInterval = setInterval(() => {
      const connection = this.connections.get(connectionId);
      
      if (!connection || !connection.isActive) {
        clearInterval(heartbeatInterval);
        return;
      }

      this.sendEvent(connectionId, {
        type: 'heartbeat',
        data: { timestamp: new Date().toISOString() }
      });
    }, 30000); // Send heartbeat every 30 seconds
  }

  /**
   * Sends missed notifications since lastEventId
   */
  private async sendMissedNotifications(connectionId: string, lastEventId: string): Promise<void> {
    // This would typically query the database for notifications
    // created after the lastEventId timestamp
    // For now, we'll just log that we received a lastEventId
    logger.info('Client reconnected with lastEventId', { connectionId, lastEventId });
  }

  /**
   * Extracts user ID from request (implement based on your auth system)
   */
  private extractUserId(request: FastifyRequest): string | null {
    // This is a placeholder - implement based on your authentication system
    // You might extract from JWT token, session, or query parameter
    const userId = (request.query as any)?.userId || (request.headers as any)?.['x-user-id'];
    return userId || null;
  }

  /**
   * Generates a unique connection ID
   */
  private generateConnectionId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generates a unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Gets current connection statistics
   */
  getConnectionStats(): {
    totalConnections: number;
    activeUsers: number;
    connectionsByUser: Record<string, number>;
  } {
    const connectionsByUser: Record<string, number> = {};
    
    this.userConnections.forEach((connectionIds, userId) => {
      connectionsByUser[userId] = connectionIds.size;
    });

    return {
      totalConnections: this.connections.size,
      activeUsers: this.userConnections.size,
      connectionsByUser
    };
  }

  /**
   * Closes all connections (for graceful shutdown)
   */
  closeAllConnections(): void {
    logger.info('Closing all SSE connections', { 
      totalConnections: this.connections.size 
    });

    this.connections.forEach((connection) => {
      this.handleDisconnection(connection.id);
    });
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let sseInstance: SSEService | null = null;

/**
 * Gets the singleton instance of the SSE service
 */
export const getSSEService = (): SSEService => {
  if (!sseInstance) {
    sseInstance = new SSEService();
  }
  return sseInstance;
};
