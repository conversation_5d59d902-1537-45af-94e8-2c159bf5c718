import { z } from 'zod';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Environment validation schema
const envSchema = z.object({
  // Application
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z.string().default('3000').transform(Number),
  COOKIE_SECRET: z
    .string()
    .min(32, 'COOKIE_SECRET must be at least 32 characters')
    .default(
      'your-super-secret-cookie-key-change-in-production-minimum-32-characters'
    ),

  // Database
  NOTIFICATION_DATABASE_URL: z
    .string()
    .default('postgresql://postgres:postgres123@localhost:5432/notification'),
  BACKOFFICE_DATABASE_URL: z
    .string()
    .default(
      'postgresql://postgres:postgres123@localhost:5432/nest_boilerplate?schema=public'
    ),

  // Redis (for Bull MQ)
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().default('6379').transform(Number),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().default('0').transform(Number),

  // Queue Configuration
  QUEUE_CONCURRENCY: z.string().default('5').transform(Number),
  CONTRACT_CHECK_CRON: z.string().default('0 */6 * * *'), // Every 6 hours

  // Development
  DEBUG: z
    .string()
    .default('false')
    .transform((val) => val === 'true'),
  CORS_ENABLED: z
    .string()
    .default('true')
    .transform((val) => val === 'true'),
});

// Validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.issues.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(
        `Environment validation failed:\n${missingVars.join('\n')}`
      );
    }
    throw error;
  }
};

// Export validated environment variables
export const env = parseEnv();

// Export types for TypeScript
export type Env = z.infer<typeof envSchema>;

// Helper to check if we're in development
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';
