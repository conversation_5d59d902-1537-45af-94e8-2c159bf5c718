import { Job } from 'bullmq';

// ============================================================================
// JOB DATA TYPES
// ============================================================================

/**
 * Job data for contract reading
 */
export interface ContractReaderJobData {
  batchSize?: number;
  daysBeforeExpiration?: number;
  lastProcessedId?: string;
}

/**
 * Job data for notification processing
 */
export interface NotificationProcessorJobData {
  contractId: string;
  contractType: string;
  entityType: string;
  entityUuid: string;
  expirationDate: Date;
  currentVersion: number;
  userId?: string;
  metadata?: Record<string, any>;
}

// ============================================================================
// JOB TYPES
// ============================================================================

export type ContractReaderJob = Job<ContractReaderJobData>;
export type NotificationProcessorJob = Job<NotificationProcessorJobData>;

// ============================================================================
// JOB NAMES
// ============================================================================

export const JOB_NAMES = {
  // Contract Reader Jobs
  READ_EXPIRING_CONTRACTS: 'read-expiring-contracts',
  
  // Notification Processor Jobs
  PROCESS_CONTRACT_EXPIRATION: 'process-contract-expiration',
} as const;

export type JobName = typeof JOB_NAMES[keyof typeof JOB_NAMES];

// ============================================================================
// JOB OPTIONS
// ============================================================================

export const JOB_OPTIONS = {
  [JOB_NAMES.READ_EXPIRING_CONTRACTS]: {
    priority: 10,
    delay: 0,
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 5000,
    },
  },
  [JOB_NAMES.PROCESS_CONTRACT_EXPIRATION]: {
    priority: 5,
    delay: 0,
    attempts: 5,
    backoff: {
      type: 'exponential' as const,
      delay: 2000,
    },
  },
} as const;

// ============================================================================
// QUEUE STATISTICS
// ============================================================================

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

// ============================================================================
// CONTRACT EXPIRATION NOTIFICATION TYPES
// ============================================================================

export enum ContractExpirationNotificationType {
  EXPIRING_SOON = 'CONTRACT_EXPIRING_SOON',
  EXPIRED = 'CONTRACT_EXPIRED',
  RENEWAL_REMINDER = 'CONTRACT_RENEWAL_REMINDER',
}

export interface ContractExpirationData {
  contractId: string;
  contractType: string;
  entityType: string;
  entityUuid: string;
  expirationDate: Date;
  daysUntilExpiration: number;
  currentVersion: number;
  filePath?: string;
  observations?: string;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export class QueueError extends Error {
  constructor(
    message: string,
    public readonly queueName: string,
    public readonly jobId?: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'QueueError';
  }
}

export class JobProcessingError extends Error {
  constructor(
    message: string,
    public readonly jobName: string,
    public readonly jobData: any,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'JobProcessingError';
  }
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type JobProcessor<T = any> = (job: Job<T>) => Promise<any>;

export interface QueueConfig {
  concurrency: number;
  removeOnComplete: number;
  removeOnFail: number;
  defaultJobOptions: {
    attempts: number;
    backoff: {
      type: 'exponential' | 'fixed';
      delay: number;
    };
  };
}
