.env
node_modules
dist
load-tests-output
logs


# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
.vscode
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment files
.env
.env.local
.env.*.local
!.env.example

# Docker
/docker/data

# Lock files
yarn.lock

# Arquivos de backup
backup/

/src/generated/prisma-notification
/src/generated/prisma-backoffice
