{"name": "cactus-notification-service", "version": "1.0.0", "description": "Cactus Notification Service", "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup src/index.ts --format cjs,esm --dts", "start": "node dist/index.js", "prisma:generate": "npm run prisma:generate:notification && npm run prisma:generate:backoffice", "prisma:generate:notification": "npx prisma generate --schema ./prisma-notification-database/schema.prisma", "prisma:generate:backoffice": "npx prisma generate --schema ./prisma-backoffice-database/schema.prisma", "prisma:migrate:notification": "npx prisma migrate dev --schema ./prisma-notification-database/schema.prisma", "prisma:migrate:deploy": "npx prisma migrate deploy --schema ./prisma-notification-database/schema.prisma", "prisma:migrate": "npm run prisma:migrate:notification && npm run prisma:migrate:deploy", "prisma:studio:notification": "npx prisma studio --schema ./prisma-notification-database/schema.prisma --port 5555", "prisma:studio:backoffice": "npx prisma studio --schema ./prisma-backoffice-database/schema.prisma --port 5556", "prisma:studio": "npm run prisma:studio:notification & npm run prisma:studio:backoffice"}, "dependencies": {"@fastify/cors": "^11.1.0", "@fastify/swagger": "^9.5.1", "@prisma/client": "^6.13.0", "bullmq": "^5.56.9", "date-fns": "^4.1.0", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "ioredis": "^5.7.0", "isomorphic-dompurify": "^2.26.0", "node-cron": "^4.2.1", "pg": "^8.16.3", "prisma": "^6.13.0", "prom-client": "^15.1.3", "tsup": "^8.5.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^4.0.15"}, "devDependencies": {"@types/ms": "^2.1.0", "@types/node": "^24.2.0", "@types/pg": "^8.15.5", "concurrently": "^8.2.2", "tsx": "^4.20.3", "typescript": "^5.9.2"}}