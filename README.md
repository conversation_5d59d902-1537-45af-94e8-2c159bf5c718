# Cactus Notification Service

Sistema de notificações para monitoramento de contratos vencendo, implementado com Node.js, TypeScript, Fastify, Bull MQ, PostgreSQL e Server-Sent Events (SSE).

## 🏗️ Arquitetura

### Componentes Principais

1. **Queue de Leitura de Contratos** (CRON + Bull MQ)
   - Executa via CRON (padrão: a cada 6 horas)
   - Consulta o banco PostgreSQL do Backoffice (ambiente HML)
   - Identifica contratos vencendo nos próximos 30 dias
   - Processa em lotes para otimizar performance

2. **Queue de Processamento de Notificações** (Bull MQ)
   - Recebe dados dos contratos vencendo
   - Cria notificações no banco do Cactus Notification Service
   - Emite NOTIFY no PostgreSQL após gravar

3. **Sistema de NOTIFY/LISTEN** (PostgreSQL)
   - Triggers no banco emitem eventos NOTIFY
   - Listener escuta eventos em tempo real
   - Atualiza status das notificações automaticamente

4. **Server-Sent Events (SSE)**
   - Endpoint para streaming de notificações em tempo real
   - Suporte a múltiplas conexões por usuário
   - Heartbeat para manter conexões ativas

5. **API REST**
   - Buscar notificações com paginação e filtros
   - Marcar notificações como lidas
   - Gerenciar preferências de notificação do usuário
   - Estatísticas e contadores

## 🚀 Funcionalidades

### Monitoramento de Contratos
- ✅ Detecção automática de contratos vencendo
- ✅ Classificação por urgência (vencido, vencendo em breve, renovação)
- ✅ Processamento em lotes otimizado
- ✅ Suporte a diferentes tipos de contrato e entidade

### Sistema de Notificações
- ✅ Múltiplos canais (EMAIL, SMS, PUSH, IN_APP, WEBHOOK, SLACK, DISCORD, WHATSAPP)
- ✅ Priorização automática baseada na urgência
- ✅ Prevenção de duplicatas
- ✅ Configurações personalizáveis por usuário

### Tempo Real
- ✅ Server-Sent Events para notificações instantâneas
- ✅ PostgreSQL NOTIFY/LISTEN para eventos de banco
- ✅ Reconexão automática em caso de falha
- ✅ Suporte a múltiplas conexões simultâneas

## 📋 Pré-requisitos

- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- TypeScript 5+

## 🛠️ Instalação

1. **Clone o repositório**
```bash
git clone <repository-url>
cd cactus-notification-service
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:
```env
# Application
NODE_ENV=development
PORT=3000
COOKIE_SECRET=your-super-secret-cookie-key-change-in-production-minimum-32-characters

# Database
NOTIFICATION_DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/notification
BACKOFFICE_DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/nest_boilerplate?schema=public

# Redis (for Bull MQ)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Queue Configuration
QUEUE_CONCURRENCY=5
CONTRACT_CHECK_CRON=0 */6 * * *

# Development
DEBUG=false
CORS_ENABLED=true
```

4. **Execute as migrações do banco**
```bash
npm run prisma:migrate
```

5. **Gere os clientes Prisma**
```bash
npm run prisma:generate
```

> **Nota**: Os triggers PostgreSQL para NOTIFY/LISTEN são configurados automaticamente na primeira execução do serviço.

## 🏃‍♂️ Execução

### Desenvolvimento
```bash
npm run dev
```

### Produção
```bash
npm run build
npm start
```