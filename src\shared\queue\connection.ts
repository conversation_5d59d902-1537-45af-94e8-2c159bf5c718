import { Queue, Worker, QueueEvents } from 'bullmq';
import IORedis from 'ioredis';
import { env } from '../config/env';
import logger from '../logger/logger';

// ============================================================================
// REDIS CONNECTION
// ============================================================================

const createRedisConnection = () => {
  const connection = new IORedis({
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    lazyConnect: true,
  });

  connection.on('connect', () => {
    logger.info('Redis connected successfully', {
      host: env.REDIS_HOST,
      port: env.REDIS_PORT,
      db: env.REDIS_DB,
    });
  });

  connection.on('error', (error) => {
    logger.error('Redis connection error', { error });
  });

  connection.on('close', () => {
    logger.warn('Redis connection closed');
  });

  return connection;
};

// Singleton Redis connection
let redisConnection: IORedis | null = null;

export const getRedisConnection = () => {
  if (!redisConnection) {
    redisConnection = createRedisConnection();
  }
  return redisConnection;
};

// ============================================================================
// QUEUE DEFINITIONS
// ============================================================================

export const QUEUE_NAMES = {
  CONTRACT_READER: 'contract-reader',
  NOTIFICATION_PROCESSOR: 'notification-processor',
} as const;

export type QueueName = typeof QUEUE_NAMES[keyof typeof QUEUE_NAMES];

// ============================================================================
// QUEUE INSTANCES
// ============================================================================

const queues = new Map<QueueName, Queue>();
const workers = new Map<QueueName, Worker>();
const queueEvents = new Map<QueueName, QueueEvents>();

/**
 * Creates or returns existing queue instance
 */
export const getQueue = (queueName: QueueName): Queue => {
  if (!queues.has(queueName)) {
    const queue = new Queue(queueName, {
      connection: getRedisConnection(),
      defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    queues.set(queueName, queue);
    logger.info(`Queue created: ${queueName}`);
  }

  return queues.get(queueName)!;
};

/**
 * Creates or returns existing worker instance
 */
export const getWorker = (
  queueName: QueueName,
  processor: (job: any) => Promise<any>
): Worker => {
  if (!workers.has(queueName)) {
    const worker = new Worker(queueName, processor, {
      connection: getRedisConnection(),
      concurrency: env.QUEUE_CONCURRENCY,
    });

    worker.on('completed', (job) => {
      logger.info(`Job completed: ${job.name}`, {
        jobId: job.id,
        queue: queueName,
        duration: job.finishedOn ? job.finishedOn - job.processedOn! : 0,
      });
    });

    worker.on('failed', (job, err) => {
      logger.error(`Job failed: ${job?.name}`, {
        jobId: job?.id,
        queue: queueName,
        error: err.message,
        attempts: job?.attemptsMade,
      });
    });

    worker.on('error', (err) => {
      logger.error(`Worker error in queue: ${queueName}`, { error: err });
    });

    workers.set(queueName, worker);
    logger.info(`Worker created for queue: ${queueName}`);
  }

  return workers.get(queueName)!;
};

/**
 * Creates or returns existing queue events instance
 */
export const getQueueEvents = (queueName: QueueName): QueueEvents => {
  if (!queueEvents.has(queueName)) {
    const events = new QueueEvents(queueName, {
      connection: getRedisConnection(),
    });

    events.on('waiting', ({ jobId }) => {
      logger.debug(`Job waiting: ${jobId}`, { queue: queueName });
    });

    events.on('active', ({ jobId }) => {
      logger.debug(`Job active: ${jobId}`, { queue: queueName });
    });

    queueEvents.set(queueName, events);
    logger.info(`Queue events created for: ${queueName}`);
  }

  return queueEvents.get(queueName)!;
};

// ============================================================================
// CONNECTION MANAGEMENT
// ============================================================================

/**
 * Initialize all queue connections
 */
export const connectQueues = async () => {
  try {
    logger.info('Connecting to Redis for queues...');
    
    const redis = getRedisConnection();
    await redis.connect();
    
    logger.info('Queue system initialized successfully');
  } catch (error) {
    logger.error('Failed to connect to queue system', { error });
    throw error;
  }
};

/**
 * Gracefully close all queue connections
 */
export const disconnectQueues = async () => {
  try {
    logger.info('Closing queue connections...');

    // Close all workers
    const workerPromises = Array.from(workers.values()).map(worker => worker.close());
    await Promise.all(workerPromises);

    // Close all queue events
    const eventPromises = Array.from(queueEvents.values()).map(events => events.close());
    await Promise.all(eventPromises);

    // Close all queues
    const queuePromises = Array.from(queues.values()).map(queue => queue.close());
    await Promise.all(queuePromises);

    // Close Redis connection
    if (redisConnection) {
      await redisConnection.quit();
      redisConnection = null;
    }

    // Clear maps
    workers.clear();
    queueEvents.clear();
    queues.clear();

    logger.info('All queue connections closed successfully');
  } catch (error) {
    logger.error('Error closing queue connections', { error });
    throw error;
  }
};

/**
 * Health check for queue system
 */
export const checkQueueHealth = async () => {
  try {
    const redis = getRedisConnection();
    await redis.ping();
    return { redis: true, queues: true };
  } catch (error) {
    logger.warn('Queue health check failed', { error });
    return { redis: false, queues: false };
  }
};
