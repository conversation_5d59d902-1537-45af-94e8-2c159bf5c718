-- CreateEnum
CREATE TYPE "public"."NotificationType" AS ENUM ('SYSTEM_ALERT', 'USER_MESSAGE', 'TASK_REMINDER', 'SECURITY_ALERT', 'MARKETING', 'TRANSACTION', 'SOCIAL', 'NEWS', 'CONTRACT_EXPIRING', 'CONTRACT_EXPIRED', 'CONTRACT_RENEWAL', 'CONTRACT_GENERAL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."NotificationChannel" AS ENUM ('EMAIL', 'SMS', 'PUSH', 'IN_APP', 'WEBHOOK', 'SLACK', 'DISCORD', 'WHATSAPP');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."NotificationStatus" AS ENUM ('PENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED', 'CANCELLED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateTable
CREATE TABLE "public"."user_notification_configs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "notificationType" "public"."NotificationType" NOT NULL,
    "channel" "public"."NotificationChannel" NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "emailAddress" TEXT,
    "phoneNumber" TEXT,
    "deviceToken" TEXT,
    "webhookUrl" TEXT,
    "slackChannel" TEXT,
    "discordChannel" TEXT,
    "WhatsAppNumber" TEXT,
    "quietHoursStart" TEXT,
    "quietHoursEnd" TEXT,
    "timezone" TEXT,
    "maxFrequency" INTEGER DEFAULT 0,
    "batchNotifications" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_notification_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."notifications" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "public"."NotificationType" NOT NULL,
    "priority" "public"."NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB,
    "channels" "public"."NotificationChannel"[],
    "scheduledFor" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "status" "public"."NotificationStatus" NOT NULL DEFAULT 'PENDING',
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "lastAttemptAt" TIMESTAMP(3),
    "sentAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "readAt" TIMESTAMP(3),
    "source" TEXT,
    "referenceId" TEXT,
    "tags" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."notification_deliveries" (
    "id" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "channel" "public"."NotificationChannel" NOT NULL,
    "status" "public"."NotificationStatus" NOT NULL DEFAULT 'PENDING',
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "recipientAddress" TEXT,
    "providerResponse" JSONB,
    "errorMessage" TEXT,
    "sentAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "failedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notification_deliveries_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_notification_configs_userId_idx" ON "public"."user_notification_configs"("userId");

-- CreateIndex
CREATE INDEX "user_notification_configs_notificationType_idx" ON "public"."user_notification_configs"("notificationType");

-- CreateIndex
CREATE INDEX "user_notification_configs_channel_idx" ON "public"."user_notification_configs"("channel");

-- CreateIndex
CREATE UNIQUE INDEX "user_notification_configs_userId_notificationType_channel_key" ON "public"."user_notification_configs"("userId", "notificationType", "channel");

-- CreateIndex
CREATE INDEX "notifications_userId_idx" ON "public"."notifications"("userId");

-- CreateIndex
CREATE INDEX "notifications_type_idx" ON "public"."notifications"("type");

-- CreateIndex
CREATE INDEX "notifications_status_idx" ON "public"."notifications"("status");

-- CreateIndex
CREATE INDEX "notifications_scheduledFor_idx" ON "public"."notifications"("scheduledFor");

-- CreateIndex
CREATE INDEX "notifications_createdAt_idx" ON "public"."notifications"("createdAt");

-- CreateIndex
CREATE INDEX "notifications_referenceId_idx" ON "public"."notifications"("referenceId");

-- CreateIndex
CREATE INDEX "notification_deliveries_notificationId_idx" ON "public"."notification_deliveries"("notificationId");

-- CreateIndex
CREATE INDEX "notification_deliveries_channel_idx" ON "public"."notification_deliveries"("channel");

-- CreateIndex
CREATE INDEX "notification_deliveries_status_idx" ON "public"."notification_deliveries"("status");

-- AddForeignKey
ALTER TABLE "public"."notification_deliveries" ADD CONSTRAINT "notification_deliveries_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "public"."notifications"("id") ON DELETE CASCADE ON UPDATE CASCADE;
