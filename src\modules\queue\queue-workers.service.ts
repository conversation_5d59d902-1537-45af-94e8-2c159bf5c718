import { Worker } from 'bullmq';
import { get<PERSON>orker, QUEUE_NAMES } from '../../shared/queue/connection';
import {
  ContractReaderJob,
  NotificationProcessorJob,
  JOB_NAMES,
  JobProcessingError,
} from '../../shared/queue/types';
import { ContractReaderService } from '../contracts/contract-reader.service';
import { NotificationProcessorService } from '../notifications/notification-processor.service';
import logger from '../../shared/logger/logger';

// ============================================================================
// QUEUE WORKERS SERVICE
// ============================================================================

export class QueueWorkersService {
  private contractReaderWorker: Worker | null = null;
  private notificationProcessorWorker: Worker | null = null;

  private readonly contractReaderService = new ContractReaderService();
  private readonly notificationProcessorService =
    new NotificationProcessorService();

  /**
   * Starts all queue workers
   */
  async startWorkers(): Promise<void> {
    logger.info('Starting queue workers...');

    try {
      await this.startContractReaderWorker();
      await this.startNotificationProcessorWorker();

      logger.info('All queue workers started successfully');
    } catch (error) {
      logger.error('Error starting queue workers', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Stops all queue workers
   */
  async stopWorkers(): Promise<void> {
    logger.info('Stopping queue workers...');

    const stopPromises: Promise<void>[] = [];

    if (this.contractReaderWorker) {
      stopPromises.push(this.contractReaderWorker.close());
    }

    if (this.notificationProcessorWorker) {
      stopPromises.push(this.notificationProcessorWorker.close());
    }

    try {
      await Promise.all(stopPromises);

      this.contractReaderWorker = null;
      this.notificationProcessorWorker = null;

      logger.info('All queue workers stopped successfully');
    } catch (error) {
      logger.error('Error stopping queue workers', { error });
      throw error;
    }
  }

  /**
   * Starts the contract reader worker
   */
  private async startContractReaderWorker(): Promise<void> {
    this.contractReaderWorker = getWorker(
      QUEUE_NAMES.CONTRACT_READER,
      async (job: ContractReaderJob) => {
        logger.info(`Processing contract reader job: ${job.name}`, {
          jobId: job.id,
          data: job.data,
        });

        try {
          switch (job.name) {
            case JOB_NAMES.READ_EXPIRING_CONTRACTS:
              await this.contractReaderService.processContractReading(job.data);
              break;

            default:
              throw new JobProcessingError(
                `Unknown job name: ${job.name}`,
                job.name,
                job.data
              );
          }

          logger.info(`Contract reader job completed: ${job.name}`, {
            jobId: job.id,
          });
        } catch (error) {
          logger.error(`Contract reader job failed: ${job.name}`, {
            jobId: job.id,
            error,
            data: job.data,
          });
          throw error;
        }
      }
    );

    logger.info('Contract reader worker started');
  }

  /**
   * Starts the notification processor worker
   */
  private async startNotificationProcessorWorker(): Promise<void> {
    this.notificationProcessorWorker = getWorker(
      QUEUE_NAMES.NOTIFICATION_PROCESSOR,
      async (job: NotificationProcessorJob) => {
        logger.info(`Processing notification job: ${job.name}`, {
          jobId: job.id,
          contractId: job.data.contractId,
        });

        try {
          switch (job.name) {
            case JOB_NAMES.PROCESS_CONTRACT_EXPIRATION:
              await this.notificationProcessorService.processContractExpiration(
                job.data
              );
              break;

            default:
              throw new JobProcessingError(
                `Unknown job name: ${job.name}`,
                job.name,
                job.data
              );
          }

          logger.info(`Notification job completed: ${job.name}`, {
            jobId: job.id,
            contractId: job.data.contractId,
          });
        } catch (error) {
          logger.error(`Notification job failed: ${job.name}`, {
            jobId: job.id,
            contractId: job.data.contractId,
            error,
            data: job.data,
          });
          throw error;
        }
      }
    );

    logger.info('Notification processor worker started');
  }

  /**
   * Gets the status of all workers
   */
  getWorkersStatus(): {
    contractReader: { isRunning: boolean };
    notificationProcessor: { isRunning: boolean };
  } {
    return {
      contractReader: {
        isRunning:
          this.contractReaderWorker !== null &&
          !this.contractReaderWorker.closing,
      },
      notificationProcessor: {
        isRunning:
          this.notificationProcessorWorker !== null &&
          !this.notificationProcessorWorker.closing,
      },
    };
  }

  /**
   * Gets detailed statistics for all queues
   */
  async getQueuesStatistics() {
    try {
      const contractReaderQueue = this.contractReaderWorker?.queue;
      const notificationProcessorQueue =
        this.notificationProcessorWorker?.queue;

      const [contractReaderStats, notificationProcessorStats] =
        await Promise.all([
          contractReaderQueue ? this.getQueueStats(contractReaderQueue) : null,
          notificationProcessorQueue
            ? this.getQueueStats(notificationProcessorQueue)
            : null,
        ]);

      return {
        contractReader: contractReaderStats,
        notificationProcessor: notificationProcessorStats,
      };
    } catch (error) {
      logger.error('Error getting queue statistics', { error });
      throw error;
    }
  }

  /**
   * Gets statistics for a specific queue
   */
  private async getQueueStats(queue: any) {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      isPaused: await queue.isPaused(),
    };
  }

  /**
   * Pauses all workers
   */
  async pauseWorkers(): Promise<void> {
    logger.info('Pausing all workers...');

    const pausePromises: Promise<void>[] = [];

    if (this.contractReaderWorker) {
      pausePromises.push(this.contractReaderWorker.pause());
    }

    if (this.notificationProcessorWorker) {
      pausePromises.push(this.notificationProcessorWorker.pause());
    }

    await Promise.all(pausePromises);
    logger.info('All workers paused');
  }

  /**
   * Resumes all workers
   */
  async resumeWorkers(): Promise<void> {
    logger.info('Resuming all workers...');

    const resumePromises: Promise<void>[] = [];

    if (this.contractReaderWorker) {
      resumePromises.push(this.contractReaderWorker.resume());
    }

    if (this.notificationProcessorWorker) {
      resumePromises.push(this.notificationProcessorWorker.resume());
    }

    await Promise.all(resumePromises);
    logger.info('All workers resumed');
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let workersInstance: QueueWorkersService | null = null;

/**
 * Gets the singleton instance of the queue workers service
 */
export const getQueueWorkers = (): QueueWorkersService => {
  if (!workersInstance) {
    workersInstance = new QueueWorkersService();
  }
  return workersInstance;
};
