import type { FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { ZodError } from 'zod';
import { HttpException } from '../errors/custom-exceptions';
import { HttpStatus } from '../enums/status-code';
import { sanitizeResponse } from '../utils/data-manipulation';

export const errorHandler = (
  error: FastifyError | Error,
  request: FastifyRequest,
  reply: FastifyReply
) => {
  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const responseData = {
      success: false,
      message: 'Validation failed',
      errors: error.issues.map((err) => ({
        field: err.path.join('.') || 'root',
        message: err.message,
        code: err.code,
        ...('received' in err && { received: err.received }),
        ...('expected' in err && { expected: err.expected }),
        ...(err.code === 'too_small' && {
          minimum: (err as any).minimum,
          type: (err as any).type,
        }),
        ...(err.code === 'too_big' && {
          maximum: (err as any).maximum,
          type: (err as any).type,
        }),
        ...(err.code === 'invalid_format' && {
          validation: (err as any).validation,
        }),
        ...(err.code === 'invalid_type' && {
          received: (err as any).received,
          expected: (err as any).expected,
        }),
      })),
      details: `${error.issues.length} validation error(s) found`,
    };

    reply.status(HttpStatus.BAD_REQUEST).send(sanitizeResponse(responseData));
    return;
  }

  // Handle custom HTTP exceptions
  if (error instanceof HttpException) {
    const responseData = {
      success: false,
      message: error.message,
      details: error.details,
    };
    reply.status(error.code).send(responseData);
    return;
  }

  // Handle Fastify validation errors (schema-based)
  if ('validation' in error && error.validation) {
    reply.status(HttpStatus.BAD_REQUEST);
    return {
      success: false,
      message: 'Validation error',
      errors: error.validation.map((err) => ({
        field: err.instancePath || err.params?.missingProperty || 'unknown',
        message: err.message,
      })),
    };
  }

  // Handle Fastify errors with status codes
  if ('statusCode' in error && error.statusCode) {
    reply.status(error.statusCode);
    return {
      success: false,
      message: error.message || 'Request failed',
    };
  }

  // Handle generic errors
  console.error('Unhandled error:', error.message);

  reply.status(HttpStatus.INTERNAL_SERVER_ERROR);
  return {
    success: false,
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && {
      debug: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    }),
  };
};
