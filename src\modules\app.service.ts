import {
  connectDatabases,
  disconnectDatabases,
} from '../shared/database/connection';
import { connectQueues, disconnectQueues } from '../shared/queue/connection';
import {
  setupNotificationTriggers,
  checkTriggersExist,
} from '../shared/database/setup-triggers';
import { getContractScheduler } from './contracts/contract-scheduler.service';
import { getQueueWorkers } from './queue/queue-workers.service';
import { getNotificationListener } from './notifications/notification-listener.service';
import { getSSEService } from './notifications/sse.service';
import logger from '../shared/logger/logger';

// ============================================================================
// APPLICATION SERVICE
// ============================================================================

export class AppService {
  private contractScheduler = getContractScheduler();
  private queueWorkers = getQueueWorkers();
  private notificationListener = getNotificationListener();
  private sseService = getSSEService();
  private isInitialized = false;

  /**
   * Initializes all application services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Application services already initialized');
      return;
    }

    logger.info('Initializing Cactus Notification Service...');

    try {
      // 1. Connect to databases
      await connectDatabases();
      logger.info('✓ Database connections established');

      // 2. Setup PostgreSQL triggers (if not exist)
      try {
        const triggersExist = await checkTriggersExist();
        if (!triggersExist) {
          await setupNotificationTriggers();
          logger.info('✓ PostgreSQL triggers set up');
        } else {
          logger.info('✓ PostgreSQL triggers already exist');
        }
      } catch (error) {
        logger.warn('⚠️ Could not set up PostgreSQL triggers automatically', {
          error: error instanceof Error ? error.message : String(error),
        });
        logger.info(
          '💡 You can set up triggers manually using: psql -d notification -f prisma-notification-database/migrations/add_notification_triggers.sql'
        );
      }

      // 3. Connect to Redis and initialize queues
      await connectQueues();
      logger.info('✓ Queue system initialized');

      // 4. Start queue workers (skip if tables don't exist)
      try {
        await this.queueWorkers.startWorkers();
        logger.info('✓ Queue workers started');
      } catch (error) {
        logger.warn('⚠️ Could not start queue workers', {
          error: error instanceof Error ? error.message : String(error),
        });
        logger.info(
          '💡 Queue workers will be available once database tables are created'
        );
      }

      // 5. Start PostgreSQL notification listener (skip if tables don't exist)
      try {
        await this.notificationListener.start();
        logger.info('✓ PostgreSQL notification listener started');
      } catch (error) {
        logger.warn('⚠️ Could not start notification listener', {
          error: error instanceof Error ? error.message : String(error),
        });
        logger.info(
          '💡 Notification listener will be available once database tables are created'
        );
      }

      // 6. Start contract expiration scheduler
      this.contractScheduler.start();
      logger.info('✓ Contract expiration scheduler started');

      this.isInitialized = true;
      logger.info('🚀 Cactus Notification Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application services', { error });
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Gracefully shuts down all application services
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      logger.warn('Application services not initialized');
      return;
    }

    logger.info('Shutting down Cactus Notification Service...');

    try {
      await this.cleanup();
      this.isInitialized = false;
      logger.info('✓ Cactus Notification Service shut down successfully');
    } catch (error) {
      logger.error('Error during shutdown', { error });
      throw error;
    }
  }

  /**
   * Performs cleanup of all services
   */
  private async cleanup(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];

    // Stop contract scheduler
    try {
      this.contractScheduler.stop();
      logger.info('✓ Contract scheduler stopped');
    } catch (error) {
      logger.error('Error stopping contract scheduler', { error });
    }

    // Close SSE connections
    try {
      this.sseService.closeAllConnections();
      logger.info('✓ SSE connections closed');
    } catch (error) {
      logger.error('Error closing SSE connections', { error });
    }

    // Stop notification listener
    cleanupPromises.push(
      this.notificationListener.stop().catch((error) => {
        logger.error('Error stopping notification listener', { error });
      })
    );

    // Stop queue workers
    cleanupPromises.push(
      this.queueWorkers.stopWorkers().catch((error) => {
        logger.error('Error stopping queue workers', { error });
      })
    );

    // Disconnect from queues
    cleanupPromises.push(
      disconnectQueues().catch((error) => {
        logger.error('Error disconnecting from queues', { error });
      })
    );

    // Disconnect from databases
    cleanupPromises.push(
      disconnectDatabases().catch((error) => {
        logger.error('Error disconnecting from databases', { error });
      })
    );

    await Promise.all(cleanupPromises);
  }

  /**
   * Gets the current status of all services
   */
  getStatus(): {
    isInitialized: boolean;
    services: {
      databases: boolean;
      queues: boolean;
      workers: any;
      scheduler: any;
      notificationListener: any;
      sseConnections: any;
    };
  } {
    return {
      isInitialized: this.isInitialized,
      services: {
        databases: this.isInitialized, // Simplified - could check actual connection status
        queues: this.isInitialized,
        workers: this.queueWorkers.getWorkersStatus(),
        scheduler: this.contractScheduler.getStatus(),
        notificationListener: this.notificationListener.getStatus(),
        sseConnections: this.sseService.getConnectionStats(),
      },
    };
  }

  /**
   * Performs a health check on all services
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: Record<string, boolean>;
    timestamp: string;
  }> {
    const services: Record<string, boolean> = {};
    let overallHealthy = true;

    try {
      // Check database connections
      // This would typically involve actual health checks
      services.databases = this.isInitialized;

      // Check queue system
      services.queues = this.isInitialized;

      // Check workers
      const workersStatus = this.queueWorkers.getWorkersStatus();
      services.contractReaderWorker = workersStatus.contractReader.isRunning;
      services.notificationProcessorWorker =
        workersStatus.notificationProcessor.isRunning;

      // Check scheduler
      const schedulerStatus = this.contractScheduler.getStatus();
      services.contractScheduler = schedulerStatus.isRunning;

      // Check notification listener
      const listenerStatus = this.notificationListener.getStatus();
      services.notificationListener = listenerStatus.isConnected;

      // Determine overall health
      overallHealthy = Object.values(services).every(
        (status) => status === true
      );
    } catch (error) {
      logger.error('Error during health check', { error });
      overallHealthy = false;
    }

    return {
      status: overallHealthy ? 'healthy' : 'unhealthy',
      services,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Manually triggers a contract reading job (for testing/admin purposes)
   */
  async triggerContractReading(options?: any): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Application services not initialized');
    }

    await this.contractScheduler.triggerManualExecution(options);
    logger.info('Manual contract reading triggered', { options });
  }

  /**
   * Gets queue statistics for monitoring
   */
  async getQueueStatistics(): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Application services not initialized');
    }

    return await this.queueWorkers.getQueuesStatistics();
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let appServiceInstance: AppService | null = null;

/**
 * Gets the singleton instance of the application service
 */
export const getAppService = (): AppService => {
  if (!appServiceInstance) {
    appServiceInstance = new AppService();
  }
  return appServiceInstance;
};
